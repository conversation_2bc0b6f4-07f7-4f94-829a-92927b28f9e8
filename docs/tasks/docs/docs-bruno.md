# Task: Generate API Documentation from Bruno Collections

**Category**: Documentation
**Estimated Time**: 1-2 hours
**Dependencies**: None
**Priority**: Medium

## Objective
Generate comprehensive API documentation in the `./docs-api` directory based on Bruno API collection files located in the `docs-api/` directory. The documentation should be well-structured, complete, and follow the project's existing documentation standards.

## Input Sources

### Bruno Collection Structure
- **Location**: `docs-api/` directory
- **Collection File**: `docs-api/bruno.json` (main collection configuration)
- **Environment Files**: `docs-api/environments/[wnapi] LOCAL.bru`
- **Module Directories**:
  - `docs-api/Auth Module/` (10 endpoints)
  - `docs-api/Cart Module/` (5 endpoints)
  - `docs-api/File Module/` (3 endpoints)
  - `docs-api/Notification Module/` (7 endpoints)
  - `docs-api/Order Module/` (4 endpoints)
  - `docs-api/Payment Module/` (3 endpoints)
  - `docs-api/Product Module/` (15 endpoints)
  - `docs-api/RBAC module/` (endpoints to be discovered)
  - `docs-api/User Module/` (4 endpoints)

### Bruno File Format
Each `.bru` file contains:
- **meta**: name, type, sequence
- **HTTP method block**: url, body type, auth type
- **headers**: authorization and other headers
- **body**: JSON, form data, or multipart form
- **docs**: title and description
- **response**: example response data

## Output Structure

### Target Directory: `./docs-api/`
```
docs-api/
├── README.md                    # Main API documentation index
├── authentication.md            # Auth & authorization guide
├── modules/                     # Module-specific documentation
│   ├── auth.md                 # Auth Module endpoints
│   ├── cart.md                 # Cart Module endpoints
│   ├── file.md                 # File Module endpoints
│   ├── notification.md         # Notification Module endpoints
│   ├── order.md                # Order Module endpoints
│   ├── payment.md              # Payment Module endpoints
│   ├── product.md              # Product Module endpoints
│   ├── rbac.md                 # RBAC Module endpoints
│   └── user.md                 # User Module endpoints
├── examples/                    # Code examples and usage
│   ├── authentication.md       # Auth examples
│   ├── error-handling.md       # Error response examples
│   └── pagination.md           # Pagination examples
└── schemas/                     # Data schemas and models
    ├── common.md               # Common response structures
    ├── auth.md                 # Auth-related schemas
    ├── product.md              # Product-related schemas
    └── [other-modules].md      # Other module schemas
```

## Requirements

### 1. Documentation Standards
- **Language**: Vietnamese for descriptions, English for technical terms
- **Format**: Markdown with consistent structure
- **Code Examples**: Include request/response examples from Bruno files
- **Error Handling**: Document error responses and status codes
- **Authentication**: Include Bearer token requirements where applicable

### 2. Content Requirements
- **Complete Coverage**: All Bruno endpoints must be documented
- **Accurate Examples**: Use actual request/response data from Bruno files
- **Consistent Format**: Follow the pattern established in `docs/docs-api.md`
- **Multi-tenant Context**: Include tenant-related headers and considerations
- **Pagination**: Document cursor-based pagination for list endpoints

### 3. Organization Requirements
- **Module-based**: Group endpoints by functional modules
- **Logical Ordering**: Order endpoints logically (CRUD operations: Create, Read, Update, Delete)
- **Cross-references**: Link related endpoints and schemas
- **Navigation**: Include table of contents and clear section headers

## Implementation Steps

### Phase 1: Setup and Analysis (15 minutes)
1. **Scan Bruno Collections**
   - Read all `.bru` files in `docs-api/` subdirectories
   - Extract endpoint metadata (method, path, auth, description)
   - Identify common patterns and response structures
   - Map endpoints to functional modules

2. **Analyze Existing Documentation**
   - Review `docs/docs-api.md` for format standards
   - Identify response structure patterns
   - Note authentication and authorization patterns
   - Document error response formats

### Phase 2: Generate Module Documentation (60 minutes)
1. **Create Module Files**
   - Generate one markdown file per module in `docs-api/modules/`
   - Extract endpoint details from corresponding Bruno module directories
   - Include method, path, description, request/response examples
   - Maintain consistent formatting across all modules

2. **Document Each Endpoint**
   - **Header**: Method and path (e.g., `**POST** /api/v1/auth/login`)
   - **Description**: From Bruno `docs.desc` field
   - **Request Section**: Headers, body, parameters
   - **Response Section**: Success and error examples
   - **Authentication**: Required tokens and permissions

3. **Include Code Examples**
   - Extract request bodies from Bruno `body:json` sections
   - Extract response examples from Bruno `response` sections
   - Format as proper JSON code blocks
   - Include curl examples where appropriate

### Phase 3: Create Supporting Documentation (30 minutes)
1. **Main README.md**
   - Overview of the API
   - Base URL and versioning
   - Authentication overview
   - Module index with links
   - Getting started guide

2. **Authentication Guide**
   - Token-based authentication flow
   - Login/logout procedures
   - Token refresh mechanism
   - Multi-tenant considerations

3. **Common Schemas**
   - Standard response structure
   - Error response format
   - Pagination metadata
   - Common data types

### Phase 4: Quality Assurance (15 minutes)
1. **Validation**
   - Verify all Bruno endpoints are documented
   - Check for consistent formatting
   - Validate JSON examples syntax
   - Ensure proper internal linking

2. **Organization**
   - Verify logical file structure
   - Check navigation and cross-references
   - Ensure consistent naming conventions
   - Validate markdown syntax

## Expected Output Files

### Core Documentation Files
- `docs-api/README.md` - Main API documentation index
- `docs-api/authentication.md` - Authentication and authorization guide
- `docs-api/modules/auth.md` - Auth module (Register, Login, Logout, etc.)
- `docs-api/modules/cart.md` - Cart module (Get, Add, Update, Remove, Clear)
- `docs-api/modules/file.md` - File module (Upload, Get, Delete)
- `docs-api/modules/notification.md` - Notification module (Get, Mark as read, etc.)
- `docs-api/modules/order.md` - Order module (Create, Get, Update status)
- `docs-api/modules/payment.md` - Payment module (Process, Get, Refund)
- `docs-api/modules/product.md` - Product module (CRUD, Search, Categories, Reviews)
- `docs-api/modules/rbac.md` - RBAC module (Roles, Permissions)
- `docs-api/modules/user.md` - User module (CRUD operations)

### Supporting Files
- `docs-api/examples/authentication.md` - Auth flow examples
- `docs-api/examples/error-handling.md` - Error response examples
- `docs-api/examples/pagination.md` - Pagination examples
- `docs-api/schemas/common.md` - Common response structures
- `docs-api/schemas/auth.md` - Auth-related data schemas
- `docs-api/schemas/product.md` - Product-related data schemas

## Acceptance Criteria

### Completeness
- [ ] All Bruno endpoints from all modules are documented
- [ ] Each endpoint includes method, path, description, request/response examples
- [ ] Authentication requirements are clearly specified
- [ ] Error responses are documented with appropriate status codes

### Quality
- [ ] Documentation follows consistent markdown formatting
- [ ] JSON examples are properly formatted and valid
- [ ] Vietnamese descriptions are clear and professional
- [ ] Technical terms use appropriate English terminology

### Organization
- [ ] Files are organized in logical directory structure
- [ ] Module documentation is grouped appropriately
- [ ] Cross-references and navigation links work correctly
- [ ] Table of contents and indexes are complete

### Accuracy
- [ ] Request/response examples match Bruno collection data
- [ ] HTTP methods and paths are correct
- [ ] Authentication headers are properly documented
- [ ] Multi-tenant considerations are included where relevant

### Standards Compliance
- [ ] Follows existing project documentation patterns
- [ ] Consistent with `docs/docs-api.md` format
- [ ] Includes proper error handling documentation
- [ ] Maintains cursor-based pagination documentation standards

## Notes

### Technical Considerations
- **Response Format**: All APIs use standardized response structure with `status`, `data`, and `meta` fields
- **Authentication**: Bearer token authentication with multi-tenant support
- **Pagination**: Cursor-based pagination for list endpoints
- **Error Handling**: Consistent error response format with error codes

### Documentation Standards
- **Language**: Use Vietnamese for user-facing descriptions, English for technical terms
- **Code Blocks**: Use proper syntax highlighting for JSON, HTTP, and other code
- **Examples**: Include realistic data in examples, not placeholder text
- **Links**: Use relative links for internal documentation references

### Future Maintenance
- **Automation**: Consider creating scripts to auto-generate documentation from Bruno files
- **Validation**: Implement checks to ensure Bruno collections and documentation stay in sync
- **Updates**: Establish process for updating documentation when APIs change
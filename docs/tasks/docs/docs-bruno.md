# Nhiệm vụ: <PERSON><PERSON><PERSON> tà<PERSON> li<PERSON>u API từ Bruno Collections

**<PERSON><PERSON> mụ<PERSON>**: <PERSON><PERSON><PERSON> li<PERSON>
**Thời gian ước t<PERSON>h**: 1-2 giờ
**<PERSON><PERSON> thuộc**: <PERSON><PERSON><PERSON><PERSON> có
**Đ<PERSON> ưu tiên**: Trung bình

## Mục tiêu
Tạo tài liệu API toàn diện trong thư mục `./docs-api` dựa trên các file Bruno API collection nằm trong thư mục `docs-api/`. Tài liệu cần có cấu trúc tốt, đầy đủ và tuân theo các tiêu chuẩn tài liệu hiện có của dự án.

## Nguồn dữ liệu đầu vào

### Cấu trúc <PERSON> Collection
- **Vị trí**: Th<PERSON> mục `docs-api/`
- **File Collection**: `docs-api/bruno.json` (cấu hình collection chính)
- **File Environment**: `docs-api/environments/[wnapi] LOCAL.bru`
- **Th<PERSON> mục <PERSON>**:
  - `docs-api/Auth Module/` (10 endpoints)
  - `docs-api/Cart Module/` (5 endpoints)
  - `docs-api/File Module/` (3 endpoints)
  - `docs-api/Notification Module/` (7 endpoints)
  - `docs-api/Order Module/` (4 endpoints)
  - `docs-api/Payment Module/` (3 endpoints)
  - `docs-api/Product Module/` (15 endpoints)
  - `docs-api/RBAC module/` (endpoints cần khám phá)
  - `docs-api/User Module/` (4 endpoints)

### Định dạng file Bruno
Mỗi file `.bru` chứa:
- **meta**: tên, loại, thứ tự
- **HTTP method block**: url, loại body, loại auth
- **headers**: authorization và các headers khác
- **body**: JSON, form data, hoặc multipart form
- **docs**: tiêu đề và mô tả
- **response**: dữ liệu response mẫu

## Cấu trúc đầu ra

### Thư mục đích: `./docs-api/`
```
docs-api/
├── README.md                    # Trang chủ tài liệu API
├── authentication.md            # Hướng dẫn xác thực & phân quyền
├── modules/                     # Tài liệu theo module
│   ├── auth.md                 # Endpoints Auth Module
│   ├── cart.md                 # Endpoints Cart Module
│   ├── file.md                 # Endpoints File Module
│   ├── notification.md         # Endpoints Notification Module
│   ├── order.md                # Endpoints Order Module
│   ├── payment.md              # Endpoints Payment Module
│   ├── product.md              # Endpoints Product Module
│   ├── rbac.md                 # Endpoints RBAC Module
│   └── user.md                 # Endpoints User Module
├── examples/                    # Ví dụ code và cách sử dụng
│   ├── authentication.md       # Ví dụ xác thực
│   ├── error-handling.md       # Ví dụ xử lý lỗi
│   └── pagination.md           # Ví dụ phân trang
└── schemas/                     # Schemas và models dữ liệu
    ├── common.md               # Cấu trúc response chung
    ├── auth.md                 # Schemas liên quan đến auth
    ├── product.md              # Schemas liên quan đến product
    └── [other-modules].md      # Schemas của các module khác
```

## Yêu cầu

### 1. Tiêu chuẩn tài liệu
- **Ngôn ngữ**: Tiếng Việt cho mô tả, tiếng Anh cho thuật ngữ kỹ thuật
- **Định dạng**: Markdown với cấu trúc nhất quán
- **Ví dụ code**: Bao gồm ví dụ request/response từ file Bruno
- **Xử lý lỗi**: Tài liệu hóa error responses và status codes
- **Xác thực**: Bao gồm yêu cầu Bearer token khi cần thiết

### 2. Yêu cầu nội dung
- **Bao phủ đầy đủ**: Tất cả endpoints Bruno phải được tài liệu hóa
- **Ví dụ chính xác**: Sử dụng dữ liệu request/response thực tế từ file Bruno
- **Định dạng nhất quán**: Tuân theo pattern đã thiết lập trong `docs/docs-api.md`
- **Ngữ cảnh multi-tenant**: Bao gồm headers và cân nhắc liên quan đến tenant
- **Phân trang**: Tài liệu hóa cursor-based pagination cho list endpoints

### 3. Yêu cầu tổ chức
- **Theo module**: Nhóm endpoints theo module chức năng
- **Thứ tự logic**: Sắp xếp endpoints theo logic (CRUD operations: Create, Read, Update, Delete)
- **Tham chiếu chéo**: Liên kết các endpoints và schemas liên quan
- **Điều hướng**: Bao gồm mục lục và tiêu đề section rõ ràng

## Các bước thực hiện

### Giai đoạn 1: Thiết lập và phân tích (15 phút)
1. **Quét Bruno Collections**
   - Đọc tất cả file `.bru` trong các thư mục con của `docs-api/`
   - Trích xuất metadata endpoint (method, path, auth, description)
   - Xác định các pattern chung và cấu trúc response
   - Ánh xạ endpoints với các module chức năng

2. **Phân tích tài liệu hiện có**
   - Xem xét `docs/docs-api.md` để hiểu tiêu chuẩn định dạng
   - Xác định các pattern cấu trúc response
   - Ghi chú các pattern xác thực và phân quyền
   - Tài liệu hóa các định dạng error response

### Giai đoạn 2: Tạo tài liệu module (60 phút)
1. **Tạo file module**
   - Tạo một file markdown cho mỗi module trong `docs-api/modules/`
   - Trích xuất chi tiết endpoint từ thư mục Bruno module tương ứng
   - Bao gồm method, path, description, ví dụ request/response
   - Duy trì định dạng nhất quán trên tất cả modules

2. **Tài liệu hóa từng endpoint**
   - **Header**: Method và path (ví dụ: `**POST** /api/v1/auth/login`)
   - **Mô tả**: Từ trường `docs.desc` của Bruno
   - **Section Request**: Headers, body, parameters
   - **Section Response**: Ví dụ success và error
   - **Xác thực**: Tokens và permissions cần thiết

3. **Bao gồm ví dụ code**
   - Trích xuất request bodies từ sections `body:json` của Bruno
   - Trích xuất ví dụ response từ sections `response` của Bruno
   - Định dạng thành JSON code blocks phù hợp
   - Bao gồm ví dụ curl khi thích hợp

### Giai đoạn 3: Tạo tài liệu hỗ trợ (30 phút)
1. **README.md chính**
   - Tổng quan về API
   - Base URL và versioning
   - Tổng quan xác thực
   - Chỉ mục module với links
   - Hướng dẫn bắt đầu

2. **Hướng dẫn xác thực**
   - Luồng xác thực dựa trên token
   - Quy trình login/logout
   - Cơ chế refresh token
   - Cân nhắc multi-tenant

3. **Schemas chung**
   - Cấu trúc response tiêu chuẩn
   - Định dạng error response
   - Metadata phân trang
   - Các kiểu dữ liệu chung

### Giai đoạn 4: Đảm bảo chất lượng (15 phút)
1. **Validation**
   - Xác minh tất cả endpoints Bruno đã được tài liệu hóa
   - Kiểm tra định dạng nhất quán
   - Validate cú pháp JSON examples
   - Đảm bảo liên kết nội bộ phù hợp

2. **Tổ chức**
   - Xác minh cấu trúc file logic
   - Kiểm tra điều hướng và tham chiếu chéo
   - Đảm bảo quy ước đặt tên nhất quán
   - Validate cú pháp markdown

## File đầu ra dự kiến

### File tài liệu cốt lõi
- `docs-api/README.md` - Trang chủ tài liệu API chính
- `docs-api/authentication.md` - Hướng dẫn xác thực và phân quyền
- `docs-api/modules/auth.md` - Auth module (Register, Login, Logout, v.v.)
- `docs-api/modules/cart.md` - Cart module (Get, Add, Update, Remove, Clear)
- `docs-api/modules/file.md` - File module (Upload, Get, Delete)
- `docs-api/modules/notification.md` - Notification module (Get, Mark as read, v.v.)
- `docs-api/modules/order.md` - Order module (Create, Get, Update status)
- `docs-api/modules/payment.md` - Payment module (Process, Get, Refund)
- `docs-api/modules/product.md` - Product module (CRUD, Search, Categories, Reviews)
- `docs-api/modules/rbac.md` - RBAC module (Roles, Permissions)
- `docs-api/modules/user.md` - User module (CRUD operations)

### File hỗ trợ
- `docs-api/examples/authentication.md` - Ví dụ luồng xác thực
- `docs-api/examples/error-handling.md` - Ví dụ error response
- `docs-api/examples/pagination.md` - Ví dụ phân trang
- `docs-api/schemas/common.md` - Cấu trúc response chung
- `docs-api/schemas/auth.md` - Data schemas liên quan đến auth
- `docs-api/schemas/product.md` - Data schemas liên quan đến product

## Tiêu chí chấp nhận

### Tính đầy đủ
- [ ] Tất cả endpoints Bruno từ tất cả modules đã được tài liệu hóa
- [ ] Mỗi endpoint bao gồm method, path, description, ví dụ request/response
- [ ] Yêu cầu xác thực được chỉ định rõ ràng
- [ ] Error responses được tài liệu hóa với status codes phù hợp

### Chất lượng
- [ ] Tài liệu tuân theo định dạng markdown nhất quán
- [ ] Ví dụ JSON được định dạng đúng và hợp lệ
- [ ] Mô tả tiếng Việt rõ ràng và chuyên nghiệp
- [ ] Thuật ngữ kỹ thuật sử dụng tiếng Anh phù hợp

### Tổ chức
- [ ] Files được tổ chức trong cấu trúc thư mục logic
- [ ] Tài liệu module được nhóm phù hợp
- [ ] Tham chiếu chéo và navigation links hoạt động đúng
- [ ] Mục lục và chỉ mục đầy đủ

### Độ chính xác
- [ ] Ví dụ request/response khớp với dữ liệu Bruno collection
- [ ] HTTP methods và paths chính xác
- [ ] Authentication headers được tài liệu hóa đúng
- [ ] Cân nhắc multi-tenant được bao gồm khi liên quan

### Tuân thủ tiêu chuẩn
- [ ] Tuân theo patterns tài liệu dự án hiện có
- [ ] Nhất quán với định dạng `docs/docs-api.md`
- [ ] Bao gồm tài liệu xử lý lỗi phù hợp
- [ ] Duy trì tiêu chuẩn tài liệu cursor-based pagination

## Ghi chú

### Cân nhắc kỹ thuật
- **Định dạng Response**: Tất cả APIs sử dụng cấu trúc response tiêu chuẩn với các trường `status`, `data`, và `meta`
- **Xác thực**: Xác thực Bearer token với hỗ trợ multi-tenant
- **Phân trang**: Cursor-based pagination cho list endpoints
- **Xử lý lỗi**: Định dạng error response nhất quán với error codes

### Tiêu chuẩn tài liệu
- **Ngôn ngữ**: Sử dụng tiếng Việt cho mô tả người dùng, tiếng Anh cho thuật ngữ kỹ thuật
- **Code Blocks**: Sử dụng syntax highlighting phù hợp cho JSON, HTTP, và code khác
- **Ví dụ**: Bao gồm dữ liệu thực tế trong ví dụ, không phải placeholder text
- **Links**: Sử dụng relative links cho tham chiếu tài liệu nội bộ

### Bảo trì tương lai
- **Tự động hóa**: Cân nhắc tạo scripts để tự động tạo tài liệu từ file Bruno
- **Validation**: Triển khai kiểm tra để đảm bảo Bruno collections và tài liệu đồng bộ
- **Cập nhật**: Thiết lập quy trình cập nhật tài liệu khi APIs thay đổi
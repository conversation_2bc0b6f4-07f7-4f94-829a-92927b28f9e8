# Kế hoạch Triển khai Hệ thống Phân quyền RBAC

## Tổng quan

Tài liệu này mô tả kế hoạch chi tiết để triển khai hệ thống phân quyền RBAC (Role-Based Access Control) cho ứng dụng wnapi. Hệ thống này sẽ được xây dựng theo cách tiếp cận từng bước, đảm bảo rằng sau mỗi task, ứng dụng vẫn hoạt động bình thường mà không bị gián đoạn.

## Kiến trúc tổng thể

Hệ thống phân quyền sẽ bao gồm các thành phần chính:

1. **Package `internal/pkg/permission`**: Cung cấp các interface, utils, và middleware factory được chia sẻ bởi tất cả các module.
2. **Module RBAC**: Triển khai các service và repository thực tế kiểm tra quyền thông qua database.
3. **Cache Layer**: <PERSON>i<PERSON>m tải cho database bằng cách cache kết quả kiểm tra quyền.
4. **<PERSON><PERSON> thống Bootstrap**: Khởi tạo và kết nối các thành phần lại với nhau.
5. **Tích hợp vào modules**: Áp dụng middleware phân quyền cho routes cần bảo vệ.

## Danh sách Task

### Task 01: Thiết lập Package Permission Cơ bản
- **Mục tiêu**: Tạo cấu trúc thư mục và các file cơ bản cho package `internal/pkg/permission`.
- **Kết quả**: Package cơ sở với các interface và constants được định nghĩa.
- **Thời gian**: 2-3 giờ
- **Độ ưu tiên**: Cao (Task đầu tiên)

### Task 02: Triển khai Xử lý Lỗi cho Hệ thống Phân quyền
- **Mục tiêu**: Xây dựng hệ thống xử lý lỗi chuẩn hóa cho package permission.
- **Kết quả**: Hệ thống lỗi nhất quán, tích hợp với response formatter của ứng dụng.
- **Thời gian**: 2-3 giờ
- **Độ ưu tiên**: Cao
- **Phụ thuộc**: Task 01

### Task 03: Triển khai MiddlewareFactory
- **Mục tiêu**: Xây dựng `MiddlewareFactory` - thành phần tạo middleware kiểm tra quyền.
- **Kết quả**: MiddlewareFactory đầy đủ với các phương thức tạo middleware.
- **Thời gian**: 3-4 giờ
- **Độ ưu tiên**: Cao
- **Phụ thuộc**: Task 01, 02

### Task 04: Triển khai Cache cho Permission
- **Mục tiêu**: Xây dựng lớp cache cho hệ thống permission để tối ưu hiệu năng.
- **Kết quả**: CachedPermissionChecker hoạt động với cơ chế invalidate cache.
- **Thời gian**: 3-4 giờ
- **Độ ưu tiên**: Trung bình
- **Phụ thuộc**: Task 01, 02, 03

### Task 05: Triển khai RBAC Permission Service
- **Mục tiêu**: Xây dựng service kiểm tra quyền người dùng thông qua database.
- **Kết quả**: RBACPermissionService đầy đủ với các repository hỗ trợ.
- **Thời gian**: 4-5 giờ
- **Độ ưu tiên**: Cao
- **Phụ thuộc**: Task 01

### Task 06: Triển khai Bootstrap và Kết nối Components
- **Mục tiêu**: Khởi tạo và kết nối các thành phần của hệ thống phân quyền.
- **Kết quả**: Hệ thống bootstrap đầy đủ, các thành phần kết nối đúng.
- **Thời gian**: 3-4 giờ
- **Độ ưu tiên**: Cao
- **Phụ thuộc**: Task 01, 02, 03, 04, 05

### Task 07: Tích hợp Phân quyền vào Module Chức năng
- **Mục tiêu**: Tích hợp hệ thống phân quyền vào các module của ứng dụng.
- **Kết quả**: Các module áp dụng middleware phân quyền cho routes.
- **Thời gian**: 4-5 giờ
- **Độ ưu tiên**: Cao
- **Phụ thuộc**: Task 01, 02, 03, 04, 05, 06

## Thời gian dự kiến

Tổng thời gian dự kiến cho toàn bộ quá trình triển khai là khoảng 21-28 giờ làm việc. Tuy nhiên, thời gian thực tế có thể khác nhau tùy thuộc vào độ phức tạp của mã hiện có và sự tích hợp giữa các thành phần.

## Kế hoạch triển khai

### Giai đoạn 1: Xây dựng Cơ sở hạ tầng (Task 01-04)
Triển khai các thành phần cơ bản của hệ thống phân quyền như interfaces, constants, error handling, middleware factory, và caching. Giai đoạn này tập trung vào việc xây dựng package `internal/pkg/permission`.

### Giai đoạn 2: Triển khai Logic RBAC (Task 05)
Xây dựng logic kiểm tra quyền thực tế thông qua module RBAC, bao gồm các repository và service để tương tác với database.

### Giai đoạn 3: Tích hợp và Hoàn thiện (Task 06-07)
Kết nối tất cả các thành phần lại với nhau thông qua bootstrap và tích hợp hệ thống phân quyền vào các module chức năng của ứng dụng.

## Lưu ý quan trọng

1. **Thứ tự middleware**: Luôn đảm bảo thứ tự đúng: Tenant → Auth → Permission.
2. **Quy ước đặt tên permission**: Tuân thủ format `{module}.{action}.{resource}`.
3. **Cache invalidation**: Nhớ invalidate cache khi quyền của người dùng thay đổi.
4. **Tái sử dụng code**: Tránh lặp lại logic kiểm tra quyền, sử dụng package chia sẻ.
5. **Testing**: Thêm unit tests và integration tests sau mỗi task quan trọng.

## Kết luận

Triển khai hệ thống phân quyền RBAC theo kế hoạch này sẽ cung cấp một giải pháp mạnh mẽ, linh hoạt và dễ bảo trì cho ứng dụng. Việc chia nhỏ thành các task sẽ giúp quản lý rủi ro và đảm bảo rằng ứng dụng luôn hoạt động trong quá trình triển khai. 
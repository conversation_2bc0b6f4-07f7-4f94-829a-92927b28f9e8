Ok, đã hiểu rõ yêu cầu của bạn! Chúng ta sẽ viết lại tài liệu hướng dẫn tích hợp hệ thống phân quyền RBAC một cách hoàn chỉnh, nhưng **không sử dụng `google/wire`**. Thay vào đó, tài liệu sẽ tập trung vào việc khởi tạo và kết nối các dependencies một cách thủ công.

Điều này có nghĩa là phần `wire.go`, `wire_gen.go`, các `ProviderSet` và các injector của `wire` sẽ được loại bỏ. Thay vào đó, sẽ có một phần hướng dẫn chi tiết về cách thiết lập các thành phần này trong hàm khởi tạo ứng dụng chính (ví dụ `main.go` hoặc một hàm `bootstrap`).

---

# <PERSON><PERSON> thống Phân quyền RBAC - Hướng dẫn Tích hợp <PERSON> công (wnapi)

## Mụ<PERSON> lục
1.  [Tổng quan Hệ thống Phân quyền](#1-tổng-quan-hệ-thống-phân-quyền)
    1.1. [Kiến trúc RBAC](#11-kiến-trúc-rbac)
    1.2. [Thứ tự Middleware Quan trọng](#12-thứ-tự-middleware-quan-trọng)
2.  [Quy tắc Đặt tên Permission](#2-quy-tắc-đặt-tên-permission)
    2.1. [Định dạng chuẩn](#21-định-dạng-chuẩn)
    2.2. [Các `action` chuẩn](#22-các-action-chuẩn)
    2.3. [Ví dụ đặt tên](#23-ví-dụ-đặt-tên)
3.  [Shared Permission Package: `internal/pkg/permission/`](#3-shared-permission-package-internalpkgpermission)
    3.1. [Cấu trúc Package](#31-cấu-trúc-package)
    3.2. [Interfaces và Types chính (`interfaces.go`)](#32-interfaces-và-types-chính-interfacesgo)
    3.3. [Constants và PermissionBuilder (`constants.go`)](#33-constants-và-permissionbuilder-constantsgo)
    3.4. [Xử lý Lỗi Chuẩn hóa (`errors.go`)](#34-xử-lý-lỗi-chuẩn-hóa-errorsgo)
    3.5. [Factory Tạo Middleware (`factory.go`)](#35-factory-tạo-middleware-factorygo)
    3.6. [Caching Permission (`cache.go`)](#36-caching-permission-cachego)
4.  [Khởi tạo và Kết nối Dependencies Thủ công](#4-khởi-tạo-và-kết-nối-dependencies-thủ-công)
    4.1. [Tổng quan quy trình khởi tạo](#41-tổng-quan-quy-trình-khởi-tạo)
    4.2. [Khởi tạo Dependencies Cốt lõi (Config, Logger, DB, Cache)](#42-khởi-tạo-dependencies-cốt-lõi-config-logger-db-cache)
    4.3. [Khởi tạo Module RBAC (Cung cấp `PermissionChecker`)](#43-khởi-tạo-module-rbac-cung-cấp-permissionchecker)
    4.4. [Khởi tạo các Thành phần của `internal/pkg/permission`](#44-khởi-tạo-các-thành-phần-của-internalpkgpermission)
    4.5. [Cấu trúc `core.App` (nếu có) và truyền Dependencies](#45-cấu-trúc-coreapp-nếu-có-và-truyền-dependencies)
5.  [Tích hợp vào Modules Chức năng](#5-tích-hợp-vào-modules-chức-năng)
    5.1. [Truyền `MiddlewareFactory` vào Handler của Module](#51-truyền-middlewarefactory-vào-handler-của-module)
    5.2. [Sử dụng `MiddlewareFactory` trong Routes của Module](#52-sử-dụng-middlewarefactory-trong-routes-của-module)
6.  [Benefits và Usage Patterns](#6-benefits-và-usage-patterns)
    6.1. [Lợi ích của Shared Package](#61-lợi-ích-của-shared-package)
    6.2. [Usage Patterns của Middleware](#62-usage-patterns-của-middleware)
7.  [Best Practices](#7-best-practices)
    7.1. [Nguyên tắc Bảo mật](#71-nguyên-tắc-bảo-mật)
    7.2. [Performance Optimization](#72-performance-optimization)
    7.3. [Error Handling](#73-error-handling)
    7.4. [Testing](#74-testing)
8.  [Troubleshooting](#8-troubleshooting)
    8.1. [Các lỗi thường gặp](#81-các-lỗi-thường-gặp)
    8.2. [Debug Tips](#82-debug-tips)

---

## 1. Tổng quan Hệ thống Phân quyền

### 1.1 Kiến trúc RBAC
Hệ thống RBAC (Role-Based Access Control) của wnapi sử dụng mô hình phân quyền dựa trên vai trò với kiến trúc multi-tenant:

```
User ←→ UserRole ←→ Role ←→ RolePermission ←→ Permission
  ↓                                              ↓
Tenant                                    PermissionGroup
```

-   **User**: Người dùng trong hệ thống.
-   **Role**: Vai trò (ví dụ: admin, manager, user).
-   **Permission**: Quyền cụ thể (ví dụ: `products.create`, `users.read`).
-   **PermissionGroup**: Nhóm quyền để tổ chức tốt hơn (ví dụ: "Quản lý sản phẩm", "Quản lý người dùng").
-   **Tenant**: Phân vùng dữ liệu cho kiến trúc multi-tenant, đảm bảo dữ liệu của một tenant không bị truy cập bởi tenant khác.

### 1.2 Thứ tự Middleware Quan trọng
Để hệ thống phân quyền hoạt động chính xác, thứ tự áp dụng middleware cho các route cần bảo vệ là cực kỳ quan trọng:

1.  **Tenant Middleware**: Xác định `TenantID` dựa trên request (ví dụ: từ subdomain, header, hoặc JWT payload) và đưa vào context.
2.  **Authentication Middleware (Auth)**: Xác thực người dùng (ví dụ: qua JWT), lấy `UserID` và các thông tin khác, đưa vào context.
3.  **RBAC Middleware (Permission)**: Sử dụng `TenantID` và `UserID` từ context để kiểm tra quyền truy cập của người dùng đối với một hành động/resource cụ thể.

```go
// Ví dụ trong việc định nghĩa routes sử dụng Gin Gonic
// protectedRoutes là một group các API cần được bảo vệ
protectedRoutes := apiGroup.Group("")
protectedRoutes.Use(tenantMiddleware())     // 1. Xác định tenant
protectedRoutes.Use(jwtAuthMiddleware())    // 2. Xác thực người dùng
// permissionMiddleware() sẽ được cung cấp bởi MiddlewareFactory,
// được khởi tạo thủ công và truyền vào các handler.
protectedRoutes.Use(permissionMiddleware()) // 3. Kiểm tra quyền
```

## 2. Quy tắc Đặt tên Permission

Việc đặt tên permission một cách nhất quán và có cấu trúc giúp dễ dàng quản lý và hiểu rõ phạm vi của từng quyền.

### 2.1 Định dạng chuẩn
Đề xuất định dạng: ` {module}.{action}.{resource}`

-   `{module}`: Tên module chức năng (ví dụ: `products`, `users`, `rbac`).
-   `{action}`: Hành động được thực hiện (xem mục 2.2).
-   `{resource}`: (Tùy chọn) Đối tượng cụ thể trong module mà hành động tác động đến. Nếu action áp dụng cho toàn bộ module hoặc không có resource cụ thể, phần này có thể bỏ qua hoặc module và resource có thể được gộp lại.

Nếu action không áp dụng cho một resource cụ thể trong module đó, hoặc resource đã ngầm hiểu từ module, có thể dùng:
`{module}.{action}`

### 2.2 Các `action` chuẩn
Sử dụng một bộ action chuẩn giúp thống nhất trong toàn hệ thống:
-   `create`: Tạo mới một đối tượng.
-   `read`: Đọc/xem chi tiết một đối tượng.
-   `update`: Cập nhật một đối tượng.
-   `delete`: Xóa một đối tượng.
-   `list`: Liệt kê danh sách các đối tượng.
-   `manage`: Quyền quản lý toàn diện trên module/resource (thường bao gồm tất cả các action CRUD và có thể thêm các action quản trị khác).
-   Các action đặc thù khác: `assign` (ví dụ: `rbac.user_roles.assign`), `send` (ví dụ: `notifications.send`), `approve`, `reject`, `publish`, v.v.

### 2.3 Ví dụ đặt tên
```go
// Module Product
"products.create"           // Tạo sản phẩm
"products.read"             // Xem chi tiết sản phẩm (resource "product" được ngầm hiểu)
"products.update"           // Cập nhật sản phẩm
"products.delete"           // Xóa sản phẩm
"products.list"             // Liệt kê sản phẩm
"products.manage"           // Quản lý toàn bộ sản phẩm

// Module User
"users.create"              // Tạo người dùng
"users.read"                // Xem thông tin người dùng
"users.update"              // Cập nhật người dùng
"users.delete"              // Xóa người dùng
"users.list"                // Liệt kê người dùng

// Module RBAC (ví dụ với resource rõ ràng)
"rbac.roles.create"         // Tạo vai trò (resource: roles)
"rbac.permissions.read"     // Xem quyền (resource: permissions)
"rbac.user_roles.assign"    // Gán vai trò cho người dùng (resource: user_roles)

// Module Notification
"notifications.send"        // Gửi thông báo
"notifications.templates.manage" // Quản lý các mẫu thông báo (resource: templates)
```

## 3. Shared Permission Package: `internal/pkg/permission/`

Để đảm bảo tính nhất quán, tái sử dụng code và dễ bảo trì, một package chia sẻ cho việc xử lý permission được đặt tại `internal/pkg/permission/`.

### 3.1 Cấu trúc Package
```text
internal/pkg/permission/
├── interfaces.go          # Định nghĩa interfaces chung (PermissionChecker, PermissionMiddleware)
├── errors.go             # Xử lý lỗi chuẩn hóa cho permission
├── cache.go              # Logic cache cho việc kiểm tra permission (CachedPermissionChecker)
├── constants.go          # Hằng số, action chuẩn, patterns đặt tên, PermissionBuilder
└── factory.go            # Factory để tạo các middleware kiểm tra permission (MiddlewareFactory)
```
(Không có file `wire.go` và `wire_gen.go` nữa)

### 3.2 Interfaces và Types chính (`interfaces.go`)
File này định nghĩa các hợp đồng (interfaces) mà các thành phần khác phải tuân theo.
```go
// internal/pkg/permission/interfaces.go
package permission

import (
	"context"
	"time" // Cần cho PermissionResult

	"github.com/gin-gonic/gin"
)

// PermissionChecker là interface cho việc kiểm tra quyền của người dùng.
// Implementation cụ thể của interface này thường nằm trong module RBAC,
// nơi có logic truy vấn database để xác định quyền.
type PermissionChecker interface {
	// UserHasPermission kiểm tra xem người dùng có một quyền cụ thể không.
	UserHasPermission(ctx context.Context, tenantID uint, userID uint, permissionCode string) (bool, error)

	// UserHasAnyPermission kiểm tra xem người dùng có ít nhất một trong danh sách các quyền không.
	UserHasAnyPermission(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error)

	// UserHasAllPermissions kiểm tra xem người dùng có tất cả các quyền trong danh sách không.
	UserHasAllPermissions(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error)
}

// PermissionMiddleware là interface cho việc tạo ra các Gin middleware functions
// để bảo vệ routes dựa trên quyền.
type PermissionMiddleware interface {
	RequirePermission(permission string) gin.HandlerFunc
	RequireAnyPermission(permissions ...string) gin.HandlerFunc
	RequireAllPermissions(permissions ...string) gin.HandlerFunc
}

// PermissionContext (Tùy chọn) có thể chứa thông tin context cho việc kiểm tra permission,
// hữu ích cho logging hoặc các quyết định phân quyền phức tạp hơn.
type PermissionContext struct {
	TenantID uint
	UserID   uint
	Path     string
	Method   string
}

// PermissionResult (Tùy chọn) có thể là kết quả chi tiết của việc kiểm tra quyền.
type PermissionResult struct {
	Allowed     bool
	Permission  string
	Reason      string // Lý do từ chối (nếu có)
	CheckedAt   time.Time
}
```

### 3.3 Constants và PermissionBuilder (`constants.go`)
File này chứa các hằng số và tiện ích giúp xây dựng chuỗi permission một cách nhất quán.
```go
// internal/pkg/permission/constants.go
package permission

import "fmt"

const (
	// Standard Actions
	ActionCreate = "create"
	ActionRead   = "read"
	ActionUpdate = "update"
	ActionDelete = "delete"
	ActionList   = "list"
	ActionManage = "manage"
	// Thêm các action đặc thù khác nếu cần
	ActionAssign = "assign"
	ActionSend   = "send"

	// PermissionSeparator là ký tự phân tách các phần trong chuỗi permission.
	PermissionSeparator = "."

	// Error Codes (sẽ được sử dụng trong errors.go)
	ErrorCodeAuthRequired     = "AUTHENTICATION_REQUIRED"
	ErrorCodeTenantRequired   = "TENANT_REQUIRED"
	ErrorCodePermissionDenied = "PERMISSION_DENIED"
	ErrorCodePermissionCheck  = "PERMISSION_CHECK_ERROR"
)

// PermissionBuilder là một helper struct để xây dựng chuỗi permission một cách an toàn và nhất quán.
type PermissionBuilder struct {
	module   string
	action   string
	resource string // Optional
}

// NewPermissionBuilder khởi tạo một PermissionBuilder với tên module.
func NewPermissionBuilder(module string) *PermissionBuilder {
	return &PermissionBuilder{module: module}
}

// Action thiết lập action cho permission.
func (pb *PermissionBuilder) Action(action string) *PermissionBuilder {
	pb.action = action
	return pb
}

// Resource thiết lập resource (tùy chọn) cho permission.
func (pb *PermissionBuilder) Resource(resource string) *PermissionBuilder {
	pb.resource = resource
	return pb
}

// Build tạo ra chuỗi permission hoàn chỉnh.
func (pb *PermissionBuilder) Build() string {
	if pb.module == "" || pb.action == "" {
		// Hoặc panic, hoặc trả về lỗi, hoặc log warning
	}
	if pb.resource != "" {
		return fmt.Sprintf("%s%s%s%s%s", pb.module, PermissionSeparator, pb.action, PermissionSeparator, pb.resource)
	}
	return fmt.Sprintf("%s%s%s", pb.module, PermissionSeparator, pb.action)
}

// Helper functions cho các bộ quyền CRUD phổ biến
func BuildCRUDPermissions(module string, resource ...string) []string {
	resName := ""
	if len(resource) > 0 && resource[0] != "" {
		resName = resource[0]
	}

	return []string{
		NewPermissionBuilder(module).Action(ActionCreate).Resource(resName).Build(),
		NewPermissionBuilder(module).Action(ActionRead).Resource(resName).Build(),
		NewPermissionBuilder(module).Action(ActionUpdate).Resource(resName).Build(),
		NewPermissionBuilder(module).Action(ActionDelete).Resource(resName).Build(),
		NewPermissionBuilder(module).Action(ActionList).Resource(resName).Build(),
	}
}

// Định nghĩa các hằng số permission cụ thể (ví dụ cho một module "example")
const (
    ExampleModule = "example" // Tên module

    ExampleCreatePermission = ExampleModule + PermissionSeparator + ActionCreate // "example.create"
    ExampleReadPermission   = ExampleModule + PermissionSeparator + ActionRead   // "example.read"
)
```
**Khuyến nghị:** Định nghĩa các hằng số permission (như `ExampleCreatePermission`) trong package của từng module (ví dụ `modules/product/permission_defs.go`) hoặc một package `definitions` tập trung để tránh "magic strings".

### 3.4 Xử lý Lỗi Chuẩn hóa (`errors.go`)
Sử dụng hệ thống lỗi chuẩn (`internal/pkg/errors`) kết hợp với response formatter chuẩn (`internal/pkg/response/http_response.go`) để trả về response lỗi một cách nhất quán khi việc kiểm tra permission thất bại.

```go
// internal/pkg/permission/errors.go
package permission

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"wnapi/internal/pkg/errors"
	"wnapi/internal/pkg/response"
)

const (
	// Error Codes
	ErrorCodeAuthRequired     = "AUTHENTICATION_REQUIRED"
	ErrorCodeTenantRequired   = "TENANT_REQUIRED"
	ErrorCodePermissionDenied = "PERMISSION_DENIED"
	ErrorCodePermissionCheck  = "PERMISSION_CHECK_ERROR"
)

// Domain error types định nghĩa các lỗi domain-specific
var (
	ErrAuthRequired   = errors.NewUnauthorizedError(ErrorCodeAuthRequired, "Yêu cầu xác thực")
	ErrTenantRequired = errors.NewBadRequestError(ErrorCodeTenantRequired, "Không tìm thấy thông tin tenant hoặc tenant không hợp lệ")
	ErrPermissionDenied = errors.NewForbiddenError(ErrorCodePermissionDenied, "Không có quyền truy cập")
	ErrPermissionCheck  = errors.NewInternalError(ErrorCodePermissionCheck, "Lỗi hệ thống khi kiểm tra quyền truy cập")
)

// AbortWithAuthRequired dừng request và trả về lỗi 401 yêu cầu xác thực.
func AbortWithAuthRequired(c *gin.Context) {
	response.AbortWithError(c, ErrAuthRequired)
}

// AbortWithTenantRequired dừng request và trả về lỗi yêu cầu tenant.
func AbortWithTenantRequired(c *gin.Context) {
	response.AbortWithError(c, ErrTenantRequired)
}

// AbortWithPermissionDenied dừng request và trả về lỗi 403 từ chối quyền truy cập.
func AbortWithPermissionDenied(c *gin.Context, permission string) {
	// Tạo lỗi cụ thể với thông tin permission
	permErr := errors.NewForbiddenError(
		ErrorCodePermissionDenied,
		"Không có quyền truy cập",
	)
	
	// Thêm thông tin chi tiết về permission bị từ chối
	permErr = permErr.WithDetails(errors.Detail{
		Field: "required_permission",
		Message: permission,
	})
	
	response.AbortWithError(c, permErr)
}

// AbortWithPermissionCheckError dừng request và trả về lỗi 500 do lỗi hệ thống khi kiểm tra quyền.
func AbortWithPermissionCheckError(c *gin.Context, err error) {
	// Bọc lỗi gốc vào domain error
	wrappedErr := errors.WrapError(ErrPermissionCheck, err)
	response.AbortWithError(c, wrappedErr)
}
```

Lưu ý rằng code trên giả định các package sau:

1. `internal/pkg/errors` cung cấp các hàm tạo lỗi domain như:
   - `NewUnauthorizedError`: tạo lỗi 401
   - `NewBadRequestError`: tạo lỗi 400
   - `NewForbiddenError`: tạo lỗi 403
   - `NewInternalError`: tạo lỗi 500
   - `WrapError`: bọc một lỗi gốc vào domain error
   - `Detail`: struct cho phép thêm thông tin chi tiết vào lỗi

2. `internal/pkg/response` cung cấp `AbortWithError(c *gin.Context, err error)` để trả về HTTP response lỗi một cách chuẩn hóa dựa trên domain error.

Việc sử dụng cách tiếp cận này giúp code dễ đọc hơn và đảm bảo format response lỗi nhất quán trong toàn bộ ứng dụng.
````markdown
### 3.5 Factory Tạo Middleware (`factory.go`)
`MiddlewareFactory` chịu trách nhiệm tạo ra các `gin.HandlerFunc` để kiểm tra quyền. Nó sử dụng một `PermissionChecker` (có thể đã được cache) để thực hiện việc kiểm tra.
```go
// internal/pkg/permission/factory.go
package permission

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"wnapi/internal/pkg/auth"   // Giả định package auth cung cấp các hàm GetUserIDFromContext, GetTenantIDFromContext
	"wnapi/internal/pkg/logger" // Giả định có package logger
)

// MiddlewareFactory tạo ra các middleware functions để kiểm tra quyền.
// Nó implement PermissionMiddleware interface.
type MiddlewareFactory struct {
	checker PermissionChecker // Checker này có thể là CachedPermissionChecker
	logger  logger.Logger
}

// NewMiddlewareFactory là constructor cho MiddlewareFactory.
func NewMiddlewareFactory(checker PermissionChecker, log logger.Logger) *MiddlewareFactory {
	return &MiddlewareFactory{
		checker: checker,
		logger:  log,
	}
}

// RequirePermission tạo middleware kiểm tra một quyền cụ thể.
func (mf *MiddlewareFactory) RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, tenantID, ok := mf.extractAuthContext(c)
		if !ok {
			return // Lỗi đã được xử lý trong extractAuthContext
		}

		hasPermission, err := mf.checker.UserHasPermission(c.Request.Context(), tenantID, userID, permission)
		if err != nil {
			mf.logger.ErrorContext(c.Request.Context(), "Lỗi kiểm tra quyền (UserHasPermission)",
				"error", err, "user_id", userID, "tenant_id", tenantID, "permission", permission)
			AbortWithPermissionCheckError(c, err)
			return
		}

		if !hasPermission {
			mf.logger.WarnContext(c.Request.Context(), "Từ chối quyền truy cập",
				"user_id", userID, "tenant_id", tenantID, "required_permission", permission, "path", c.Request.URL.Path)
			AbortWithPermissionDenied(c, permission)
			return
		}
		c.Next()
	}
}

// RequireAnyPermission tạo middleware kiểm tra người dùng có ít nhất một trong các quyền được liệt kê.
func (mf *MiddlewareFactory) RequireAnyPermission(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if len(permissions) == 0 {
			mf.logger.InfoContext(c.Request.Context(), "Không có quyền nào được yêu cầu trong RequireAnyPermission, cho qua.",
				"path", c.Request.URL.Path)
			c.Next()
			return
		}
		userID, tenantID, ok := mf.extractAuthContext(c)
		if !ok { return }

		hasAny, err := mf.checker.UserHasAnyPermission(c.Request.Context(), tenantID, userID, permissions)
		if err != nil {
			mf.logger.ErrorContext(c.Request.Context(), "Lỗi kiểm tra quyền (UserHasAnyPermission)", "error", err)
			AbortWithPermissionCheckError(c, err)
			return
		}
		if !hasAny {
			AbortWithPermissionDenied(c, fmt.Sprintf("any of: %v", permissions))
			return
		}
		c.Next()
	}
}

// RequireAllPermissions tạo middleware kiểm tra người dùng có tất cả các quyền được liệt kê.
func (mf *MiddlewareFactory) RequireAllPermissions(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if len(permissions) == 0 {
			mf.logger.InfoContext(c.Request.Context(), "Không có quyền nào được yêu cầu trong RequireAllPermissions, cho qua.",
				"path", c.Request.URL.Path)
			c.Next()
			return
		}
		userID, tenantID, ok := mf.extractAuthContext(c)
		if !ok { return }

		hasAll, err := mf.checker.UserHasAllPermissions(c.Request.Context(), tenantID, userID, permissions)
		if err != nil {
			mf.logger.ErrorContext(c.Request.Context(), "Lỗi kiểm tra quyền (UserHasAllPermissions)", "error", err)
			AbortWithPermissionCheckError(c, err)
			return
		}
		if !hasAll {
			AbortWithPermissionDenied(c, fmt.Sprintf("all of: %v", permissions))
			return
		}
		c.Next()
	}
}

// extractAuthContext là helper method để lấy UserID và TenantID từ Gin context.
func (mf *MiddlewareFactory) extractAuthContext(c *gin.Context) (userID uint, tenantID uint, ok bool) {
	uID, exists := auth.GetUserIDFromContext(c)
	if !exists {
		mf.logger.WarnContext(c.Request.Context(), "Không tìm thấy UserID trong context", "path", c.Request.URL.Path)
		AbortWithAuthRequired(c)
		return 0, 0, false
	}
	tID, err := auth.GetTenantIDFromContext(c)
	if err != nil {
		mf.logger.WarnContext(c.Request.Context(), "Không tìm thấy TenantID trong context", "error", err, "path", c.Request.URL.Path)
		AbortWithTenantRequired(c)
		return 0, 0, false
	}
	return uID, tID, true
}
```

### 3.6 Caching Permission (`cache.go`)
`CachedPermissionChecker` là một decorator bao bọc một `PermissionChecker` khác (thường là implementation truy vấn DB) để thêm lớp cache, giúp cải thiện hiệu năng.
```go
// internal/pkg/permission/cache.go
package permission

import (
	"context"
	"fmt"
	"strings"
	"time"

	"wnapi/internal/pkg/cache"   // Giả định có interface cache.Cache
	"wnapi/internal/pkg/logger"
)

// CachedPermissionChecker implement PermissionChecker và thêm lớp caching.
type CachedPermissionChecker struct {
	delegate PermissionChecker // PermissionChecker thực tế (ví dụ: truy vấn DB)
	cache    cache.Cache
	ttl      time.Duration
	logger   logger.Logger
}

// NewCachedPermissionChecker là constructor cho CachedPermissionChecker.
// `checker` ở đây là un-cached version (ví dụ: RBACPermissionService).
func NewCachedPermissionChecker(
	checker PermissionChecker,
	c cache.Cache,
	ttl time.Duration,
	log logger.Logger,
) *CachedPermissionChecker {
	return &CachedPermissionChecker{
		delegate: checker,
		cache:    c,
		ttl:      ttl,
		logger:   log,
	}
}

func (cpc *CachedPermissionChecker) buildCacheKey(tenantID uint, userID uint, permissionCode string) string {
	normalizedPermissionCode := strings.ReplaceAll(strings.ToLower(permissionCode), " ", "_")
	return fmt.Sprintf("perm_cache:%d:%d:%s", tenantID, userID, normalizedPermissionCode)
}

// UserHasPermission kiểm tra quyền, ưu tiên lấy từ cache.
func (cpc *CachedPermissionChecker) UserHasPermission(ctx context.Context, tenantID uint, userID uint, permissionCode string) (bool, error) {
	cacheKey := cpc.buildCacheKey(tenantID, userID, permissionCode)
	var cachedResult bool
	if val, err := cpc.cache.Get(ctx, cacheKey); err == nil {
		if bVal, ok := val.(bool); ok {
			cpc.logger.DebugContext(ctx, "Cache hit for permission", "key", cacheKey)
			return bVal, nil
		}
		cpc.logger.WarnContext(ctx, "Cache data type mismatch", "key", cacheKey)
	} else if !cpc.cache.IsErrCacheMiss(err) { // Giả định cache interface có hàm này
        cpc.logger.ErrorContext(ctx, "Lỗi khi lấy permission từ cache", "key", cacheKey, "error", err)
    } else {
         cpc.logger.DebugContext(ctx, "Cache miss for permission", "key", cacheKey)
    }

	hasPermission, err := cpc.delegate.UserHasPermission(ctx, tenantID, userID, permissionCode)
	if err != nil {
		return false, err
	}
	if errSet := cpc.cache.Set(ctx, cacheKey, hasPermission, cpc.ttl); errSet != nil {
		cpc.logger.ErrorContext(ctx, "Lỗi khi lưu permission vào cache", "key", cacheKey, "error", errSet)
	}
	return hasPermission, nil
}

// UserHasAnyPermission sử dụng UserHasPermission (đã được cache).
func (cpc *CachedPermissionChecker) UserHasAnyPermission(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	if len(permissions) == 0 { return true, nil }
	for _, p := range permissions {
		has, err := cpc.UserHasPermission(ctx, tenantID, userID, p)
		if err != nil { return false, fmt.Errorf("lỗi kiểm tra quyền '%s': %w", p, err) }
		if has { return true, nil }
	}
	return false, nil
}

// UserHasAllPermissions sử dụng UserHasPermission (đã được cache).
func (cpc *CachedPermissionChecker) UserHasAllPermissions(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	if len(permissions) == 0 { return true, nil }
	for _, p := range permissions {
		has, err := cpc.UserHasPermission(ctx, tenantID, userID, p)
		if err != nil { return false, fmt.Errorf("lỗi kiểm tra quyền '%s': %w", p, err) }
		if !has { return false, nil }
	}
	return true, nil
}

// InvalidateUserPermissions xóa cache permission của user.
func (cpc *CachedPermissionChecker) InvalidateUserPermissions(ctx context.Context, tenantID uint, userID uint) error {
	pattern := fmt.Sprintf("perm_cache:%d:%d:*", tenantID, userID)
	cpc.logger.InfoContext(ctx, "Invalidating user permissions cache", "pattern", pattern)
	return cpc.cache.DeletePattern(ctx, pattern) // Giả sử cache interface có hàm này
}
```

## 4. Khởi tạo và Kết nối Dependencies Thủ công

Khi không sử dụng `google/wire` hay bất kỳ DI framework nào khác, bạn sẽ cần phải tự tay khởi tạo các đối tượng và truyền chúng vào những nơi cần thiết. Việc này thường diễn ra trong hàm `main()` của ứng dụng hoặc một hàm khởi tạo (bootstrap) riêng biệt.

### 4.1. Tổng quan quy trình khởi tạo
Thứ tự khởi tạo rất quan trọng để đảm bảo các dependencies đã sẵn sàng khi một đối tượng khác cần đến chúng. Quy trình gợi ý:

1.  **Config**: Tải cấu hình ứng dụng (ví dụ: từ file YAML, biến môi trường).
2.  **Logger**: Khởi tạo logger để sử dụng trong toàn bộ ứng dụng.
3.  **Database (DBManager)**: Thiết lập kết nối đến cơ sở dữ liệu.
4.  **Cache**: Thiết lập kết nối đến hệ thống cache (ví dụ: Redis, Memcached, hoặc in-memory cache).
5.  **Module RBAC Dependencies**:
    *   Khởi tạo các repositories của module RBAC (ví dụ: `UserRoleRepository`, `RolePermissionRepository`), truyền DB connection và logger vào chúng.
    *   Khởi tạo service của module RBAC (ví dụ: `RBACPermissionService`), truyền các repositories và logger vào. Service này sẽ implement `permission.PermissionChecker`.
6.  **Shared Permission Package Components**:
    *   Khởi tạo `CachedPermissionChecker` (từ `internal/pkg/permission`), truyền `RBACPermissionService` (đã tạo ở bước 5), cache instance, TTL (từ config), và logger vào.
    *   Khởi tạo `MiddlewareFactory` (từ `internal/pkg/permission`), truyền `CachedPermissionChecker` (đã tạo ở trên) và logger vào.
7.  **Modules Chức năng Dependencies**:
    *   Khởi tạo repositories, services, và handlers cho từng module chức năng.
    *   Truyền `MiddlewareFactory` (đã tạo ở bước 6) vào các handlers của module chức năng để chúng có thể sử dụng middleware kiểm tra quyền.
8.  **Core Application Components**:
    *   Khởi tạo struct `core.App` (nếu có), truyền các dependencies cốt lõi như config, logger, DBManager, cache, và `MiddlewareFactory`.
    *   Khởi tạo HTTP server (ví dụ: `core.Server` hoặc `gin.Engine`), truyền config, logger, và các handlers (hoặc `core.App`) vào.
9.  **Đăng ký Routes**: Thiết lập các routes cho ứng dụng, áp dụng middleware (bao gồm cả permission middleware từ `MiddlewareFactory`).
10. **Khởi động Server**.

### 4.2. Khởi tạo Dependencies Cốt lõi (Config, Logger, DB, Cache)
Phần này phụ thuộc vào các thư viện cụ thể bạn sử dụng cho config, logging, database, và caching. Dưới đây là ví dụ giả định:

```go
// Trong cmd/server/main.go hoặc một hàm bootstrap.go

// Giả sử có các package và hàm New... tương ứng
import (
	"context"
	"log" // Logger chuẩn, có thể thay bằng logger của bạn
	"time"
	"wnapi/internal/pkg/cache/memorycache" // Ví dụ in-memory cache
	"wnapi/internal/pkg/config/viperconfig" // Ví dụ Viper cho config
	"wnapi/internal/pkg/database/gormdb"    // Ví dụ GORM cho DB
	"wnapi/internal/pkg/logger/zaplogger"   // Ví dụ Zap cho logger
    // ... các import khác
)

func bootstrapApplication() (*core.Server, func(), error) { // func() là cleanup function
	// 1. Khởi tạo Config
	cfg, err := viperconfig.NewConfigLoader().Load("./config/app.yaml") // Ví dụ
	if err != nil {
		return nil, nil, fmt.Errorf("lỗi khởi tạo config: %w", err)
	}

	// 2. Khởi tạo Logger
	appLogger, cleanupLogger, err := zaplogger.NewLogger(
		cfg.GetString("logger.level"),
		cfg.GetString("logger.format"),
	)
	if err != nil {
		// Dùng log chuẩn nếu logger chính lỗi
		log.Printf("Lỗi khởi tạo logger chính: %v. Sử dụng logger chuẩn.", err)
		// appLogger = logger.NewStdLoggerWrapper(log.Default()) // Ví dụ fallback
        return nil, nil, fmt.Errorf("lỗi khởi tạo logger: %w", err)
	}
	// defer cleanupLogger() // cleanup cho logger sẽ được gom vào cleanup chung

	appLogger.Info("Ứng dụng đang khởi tạo...")

	// 3. Khởi tạo Database
	dbManager, cleanupDB, err := gormdb.NewGORMDBManager(
		cfg.GetString("database.dsn_write"),
		cfg.GetString("database.dsn_read"), // Nếu có read replica
		appLogger,
		gormdb.Config{MaxOpenConns: cfg.GetInt("database.max_open_conns")},
	)
	if err != nil {
		appLogger.Error("Lỗi khởi tạo DB manager", "error", err)
		return nil, func() { cleanupLogger() }, fmt.Errorf("lỗi DB manager: %w", err)
	}
	// defer cleanupDB()

	// 4. Khởi tạo Cache
	// Ví dụ: In-memory cache, có thể thay bằng RedisCache, MemcachedCache
	appCache := memorycache.NewMemoryCache(
		cfg.GetDurationWithDefault("cache.default_ttl", 5*time.Minute),
		cfg.GetDurationWithDefault("cache.cleanup_interval", 10*time.Minute),
	)
	appLogger.Info("Cache initialized", "type", "in-memory")

    // ... các bước khởi tạo tiếp theo sẽ ở đây ...

    // Gom các cleanup functions
    cleanup := func() {
        appLogger.Info("Bắt đầu cleanup ứng dụng...")
        if cleanupDB != nil { cleanupDB() }
        if cleanupLogger != nil { cleanupLogger() }
        appLogger.Info("Cleanup hoàn tất.")
    }

    // Tạm thời trả về nil cho server cho đến khi hoàn thiện các bước sau
    return nil, cleanup, nil
}
```

### 4.3. Khởi tạo Module RBAC (Cung cấp `PermissionChecker`)
Module RBAC chịu trách nhiệm triển khai logic kiểm tra quyền thực tế.

**Implementation `RBACPermissionService` (ví dụ trong `modules/rbac/internal/service/permission_service.go`):**
(Giữ nguyên như trong phiên bản có `wire`, chỉ khác cách nó được khởi tạo và truyền dependencies)
```go
package service // modules/rbac/internal/service

// ... import ...

type PermissionService struct {
	userRoleRepo repository.UserRoleRepository
	rolePermRepo repository.RolePermissionRepository
	logger       logger.Logger
}

func NewRBACPermissionService(
	userRoleRepo repository.UserRoleRepository,
	rolePermRepo repository.RolePermissionRepository,
	log logger.Logger,
) *PermissionService {
	return &PermissionService{/* ... */}
}

// Implement các method của permission.PermissionChecker
// UserHasPermission, UserHasAnyPermission, UserHasAllPermissions
// ... (logic như đã mô tả trước đó) ...
```

**Khởi tạo trong hàm `bootstrapApplication()` (tiếp theo từ Mục 4.2):**
```go
// ... tiếp tục trong hàm bootstrapApplication() ...

    // -- Module RBAC Dependencies --
    // Giả sử bạn có các implementation repository cho RBAC
    // Ví dụ: modules/rbac/internal/repository/mysql/user_role_repo.go
    //         modules/rbac/internal/repository/mysql/role_permission_repo.go
    // Chúng cần DB connection (từ dbManager) và logger.

    // Lấy DB connection chính (ví dụ: write DB)
    dbWrite := dbManager.DB() // Giả sử DB() trả về *gorm.DB hoặc *sql.DB

    userRoleRepo := rbacMySQLRepo.NewMySQLUserRoleRepository(dbWrite, appLogger)
    rolePermRepo := rbacMySQLRepo.NewMySQLRolePermissionRepository(dbWrite, appLogger)
    // permissionRepo := rbacMySQLRepo.NewMySQLPermissionRepository(dbWrite, appLogger) // Nếu có
    // ... các repo RBAC khác

    // Khởi tạo service của module RBAC, đây là PermissionChecker thực tế (un-cached)
    rbacCheckerService := rbacSvc.NewRBACPermissionService(
        userRoleRepo,
        rolePermRepo,
        appLogger,
    )
    appLogger.Info("RBAC PermissionService (actual checker) initialized")

// ... các bước khởi tạo tiếp theo ...
```

### 4.4. Khởi tạo các Thành phần của `internal/pkg/permission`
Bây giờ, khởi tạo `CachedPermissionChecker` và `MiddlewareFactory` từ package `internal/pkg/permission`.

**Khởi tạo trong hàm `bootstrapApplication()` (tiếp theo):**
```go
// ... tiếp tục trong hàm bootstrapApplication() ...

    // -- Shared Permission Package Components --
    permissionCacheTTL := cfg.GetDurationWithDefault("cache.permission.ttl", 5*time.Minute)

    // Khởi tạo CachedPermissionChecker, truyền rbacCheckerService làm delegate
    cachedPermChecker := sharedPermission.NewCachedPermissionChecker(
        rbacCheckerService, // Đây là PermissionChecker thực tế từ module RBAC
        appCache,
        permissionCacheTTL,
        appLogger,
    )
    appLogger.Info("CachedPermissionChecker initialized")

    // Khởi tạo MiddlewareFactory, truyền cachedPermChecker
    permissionMiddlewareFactory := sharedPermission.NewMiddlewareFactory(
        cachedPermChecker, // Middleware sẽ sử dụng checker đã được cache
        appLogger,
    )
    appLogger.Info("Permission MiddlewareFactory initialized")

// ... các bước khởi tạo tiếp theo ...
```

### 4.5. Cấu trúc `core.App` (nếu có) và truyền Dependencies
Nếu bạn có một struct `core.App` để gom các thành phần chung, hãy khởi tạo và truyền dependencies vào đó.

**`internal/core/app.go` (ví dụ):**
```go
package core

import (
	// ... các import tương tự phiên bản có wire ...
	"wnapi/internal/pkg/permission"
)

type App struct {
	Config            config.Config
	Logger            logger.Logger
	DBManager         *database.DBManager
	Cache             cache.Cache
	MiddlewareFactory *permission.MiddlewareFactory // Quan trọng
	// ... các fields khác
}

// NewApp là constructor thủ công
func NewApp(
	cfg config.Config,
	log logger.Logger,
	dbm *database.DBManager,
	appCache cache.Cache,
	mwFactory *permission.MiddlewareFactory,
) *App {
	return &App{
		Config:            cfg,
		Logger:            log,
		DBManager:         dbm,
		Cache:             appCache,
		MiddlewareFactory: mwFactory,
	}
}
// Các getter nếu cần, ví dụ:
func (a *App) GetMiddlewareFactory() *permission.MiddlewareFactory { return a.MiddlewareFactory }
func (a *App) GetLogger() logger.Logger { return a.Logger }
// ...
```

**Khởi tạo `core.App` trong hàm `bootstrapApplication()` (tiếp theo):**
```go
// ... tiếp tục trong hàm bootstrapApplication() ...
    // -- Core Application Components --
    appInstance := core.NewApp(
        cfg,
        appLogger,
        dbManager,
        appCache,
        permissionMiddlewareFactory, // Truyền MiddlewareFactory đã tạo
    )
    appLogger.Info("Core App instance initialized")

    // Khởi tạo HTTP Server (ví dụ: core.Server hoặc gin.Engine trực tiếp)
    // Server sẽ cần appInstance hoặc các dependencies cụ thể như appLogger, cfg,
    // và các handlers của module (sẽ được khởi tạo ở bước tiếp theo).

    // Ví dụ khởi tạo Server và truyền appInstance
    httpServer := core.NewServer(
        appInstance, // Server có thể lấy config, logger, MiddlewareFactory từ App
        // Hoặc truyền trực tiếp: cfg, appLogger, permissionMiddlewareFactory,
    )
    appLogger.Info("HTTP Server initialized")

    // Đăng ký routes cho các module chức năng (sẽ được làm trong NewServer hoặc một method của nó)
    // Ví dụ, NewServer có thể nhận một danh sách các module và gọi RegisterRoutes của chúng.
    // Hoặc các handlers được khởi tạo và truyền vào NewServer.

    // Hoàn thiện hàm bootstrap
    return httpServer, cleanup, nil
}


// Trong cmd/server/main.go
func main() {
    server, cleanup, err := bootstrapApplication()
    if err != nil {
        log.Fatalf("Lỗi khởi tạo ứng dụng: %v", err)
    }
    defer cleanup()

    // server.RegisterModuleRoutes(productModule) // Ví dụ
    // server.RegisterModuleRoutes(userModule)   // Ví dụ

    if err := server.Start(context.Background()); err != nil {
        log.Fatalf("Lỗi khi chạy server: %v", err)
    }
}
```

## 5. Tích hợp vào Modules Chức năng

### 5.1. Truyền `MiddlewareFactory` vào Handler của Module
Khi khởi tạo handler cho một module chức năng (ví dụ: Product), bạn cần truyền `permissionMiddlewareFactory` đã tạo ở trên vào constructor của handler đó.

**Ví dụ: `modules/product/internal/api/handler.go`**
```go
package api // modules/product/internal/api

import (
	"github.com/gin-gonic/gin"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission" // Để dùng MiddlewareFactory
	productService "wnapi/modules/product/internal/service"
)

type ProductHandler struct {
	service           *productService.ProductService
	middlewareFactory *permission.MiddlewareFactory // Nhận qua constructor
	logger            logger.Logger
}

// NewProductHandler là constructor thủ công.
func NewProductHandler(
	svc *productService.ProductService,
	mwFactory *permission.MiddlewareFactory, // Tham số để nhận MiddlewareFactory
	log logger.Logger,
) *ProductHandler {
	return &ProductHandler{
		service:           svc,
		middlewareFactory: mwFactory, // Lưu lại
		logger:            log,
	}
}

// RegisterRoutes đăng ký các route cho module Product.
func (h *ProductHandler) RegisterRoutes(routerGroup *gin.RouterGroup) {
	// Sử dụng h.middlewareFactory để áp dụng permission middleware
	routerGroup.POST("",
		h.middlewareFactory.RequirePermission("products.create"), // Sử dụng
		h.CreateProductHandler,
	)
	// ... các routes khác
}
// ... (các handler methods: CreateProductHandler, ListProductsHandler, ...)
```

**Khởi tạo Product Handler trong `bootstrapApplication()` (hoặc nơi phù hợp):**
```go
// ... tiếp tục trong hàm bootstrapApplication(), sau khi permissionMiddlewareFactory được tạo ...

    // -- Module Product Dependencies (Ví dụ) --
    // productRepo := productMySQLRepo.NewMySQLProductRepository(dbWrite, appLogger)
    // prodService := productSvc.NewProductService(productRepo, appLogger)

    // prodAPIHandler := productAPI.NewProductHandler(
    //     prodService,
    //     permissionMiddlewareFactory, // << Quan trọng: Truyền MiddlewareFactory vào đây
    //     appLogger,
    // )
    // appLogger.Info("Product Handler initialized")

    // Sau đó, prodAPIHandler sẽ được truyền vào NewServer hoặc một cơ chế đăng ký route.
```
Nếu bạn có nhiều module, quy trình này sẽ lặp lại: khởi tạo service và handler của module, và truyền `permissionMiddlewareFactory` vào handler.

### 5.2. Sử dụng `MiddlewareFactory` trong Routes của Module
Trong method `RegisterRoutes` của mỗi handler module (ví dụ `ProductHandler`), bạn sẽ sử dụng `this.middlewareFactory` để áp dụng các middleware kiểm tra quyền cho từng route cụ thể, như đã minh họa trong code của `ProductHandler` ở trên.

Ví dụ, nếu `core.Server` có một method để đăng ký các "module" có khả năng tự đăng ký route:
```go
// internal/core/server.go
type RoutableModule interface {
    RegisterRoutes(group *gin.RouterGroup, mwFactory *permission.MiddlewareFactory, logger logger.Logger)
}

// ... trong NewServer hoặc setupRoutes của Server ...
func (s *Server) SetupAppRoutes(modules []RoutableModule) {
    apiV1 := s.engine.Group("/api/v1")
    // Hoặc lấy mwFactory từ s.appInstance.GetMiddlewareFactory()
    mwFactory := s.appInstance.GetMiddlewareFactory()
    appLogger := s.appInstance.GetLogger()

    for _, mod := range modules {
        // Mỗi module sẽ tự đăng ký route của nó và sử dụng mwFactory được truyền vào
        mod.RegisterRoutes(apiV1, mwFactory, appLogger)
    }
}
```

## 6. Benefits và Usage Patterns

### 6.1 Lợi ích của Shared Package
-   **Code Reusability & Consistency:**
    -   Logic kiểm tra permission, caching, error handling được tập trung ở `internal/pkg/permission`.
    -   Tất cả modules sử dụng cùng một implementation, đảm bảo tính nhất quán.
-   **Centralized Maintenance:**
    -   Bug fixes và improvements chỉ cần thực hiện ở package chia sẻ.
    -   Dễ dàng cập nhật hoặc thay đổi logic phân quyền chung.
-   **Performance Optimization:**
    -   Cơ chế caching được tích hợp sẵn trong `CachedPermissionChecker`.
-   **Standardization:**
    -   Định dạng error response, logging, và naming patterns thống nhất.
-   **Đơn giản hơn nếu không cần DI phức tạp:** Việc khởi tạo thủ công có thể dễ hiểu hơn cho các dự án nhỏ hoặc khi bạn không muốn phụ thuộc vào một DI framework.

### 6.2 Usage Patterns của Middleware
Sử dụng các method của `MiddlewareFactory` để bảo vệ routes:

#### Pattern 1: Yêu cầu một quyền cụ thể
```go
// User cần có quyền "products.create"
protectedRoutes.POST("/products",
    middlewareFactory.RequirePermission("products.create"),
    productHandler.CreateProduct)
```

#### Pattern 2: Yêu cầu ít nhất một trong nhiều quyền
```go
// User cần có quyền "products.read" HOẶC "reports.product.read"
protectedRoutes.GET("/products/:id/extended-view",
    middlewareFactory.RequireAnyPermission("products.read", "reports.product.read"),
    productHandler.GetProductExtendedView)
```

#### Pattern 3: Yêu cầu tất cả các quyền trong danh sách
```go
// User cần có CẢ quyền "products.update" VÀ "products.publish_special"
protectedRoutes.PUT("/products/:id/publish-special",
    middlewareFactory.RequireAllPermissions("products.update", "products.publish_special"),
    productHandler.PublishSpecialProduct)
```

#### Pattern 4: Sử dụng `PermissionBuilder` hoặc hằng số
```go
import permDefs "wnapi/modules/product/permissions" // Giả sử có file định nghĩa permission

// Sử dụng hằng số
protectedRoutes.DELETE("/products/:id",
    middlewareFactory.RequirePermission(permDefs.ProductDelete),
    productHandler.DeleteProduct)

// Sử dụng PermissionBuilder
readProductPermission := permission.NewPermissionBuilder("products").Action(permission.ActionRead).Build()
protectedRoutes.GET("/products/:id",
    middlewareFactory.RequirePermission(readProductPermission),
    productHandler.GetProduct)
```

## 7. Best Practices
(Phần này giữ nguyên các mục con như phiên bản có `wire`, vì các best practices về bảo mật, performance, error handling, và testing cho logic RBAC là tương tự nhau, không phụ thuộc vào việc có dùng DI framework hay không.)

### 7.1 Nguyên tắc Bảo mật
1.  **Kiểm tra quyền ở Backend (Middleware Level).**
2.  **Principle of Least Privilege.**
3.  **Tenant Isolation.**
4.  **Validate Input.**
5.  **Audit Logging.**

### 7.2 Performance Optimization
1.  **Caching Permission Checks.**
2.  **Chiến lược Cache Invalidation Hiệu quả.**
3.  **Tối ưu Truy vấn Database.**
4.  **Tránh N+1 Query.**

### 7.3 Error Handling
1.  **Consistent Error Responses.**
2.  **Meaningful Error Codes và Messages.**
3.  **Logging Chi tiết.**

### 7.4 Testing
1.  **Unit Tests:**
    *   Test `PermissionBuilder`, helpers, `MiddlewareFactory` (mock `PermissionChecker`), `CachedPermissionChecker` (mock delegate và cache), `RBACPermissionService` (mock repos).
2.  **Integration Tests:**
    *   Test luồng từ route -> middleware -> service với test DB/cache.

## 8. Troubleshooting

### 8.1 Các lỗi thường gặp (Từ phía client hoặc logic)
1.  **`"AUTHENTICATION_REQUIRED"` (HTTP 401).**
2.  **`"TENANT_REQUIRED"` (HTTP 401/400/403).**
3.  **`"PERMISSION_DENIED"` (HTTP 403).**
4.  **`"PERMISSION_CHECK_ERROR"` (HTTP 500).**

### 8.2 Debug Tips
1.  **Kiểm tra Thứ tự Middleware.**
2.  **Logging Chi tiết** trong `MiddlewareFactory`, `CachedPermissionChecker`, `RBACPermissionService`.
3.  **Verify Database Data.**
4.  **Sử dụng Postman/curl.**
5.  **Debug với Breakpoints** trong quá trình khởi tạo dependencies và luồng kiểm tra quyền.
6.  **Kiểm tra khởi tạo dependencies:** Đảm bảo tất cả các đối tượng (logger, db, cache, services, middlewareFactory) được khởi tạo đúng và được truyền vào đúng nơi. Một lỗi `nil pointer dereference` thường xảy ra nếu một dependency chưa được khởi tạo mà đã bị sử dụng.

---

Tài liệu này cung cấp một hướng dẫn toàn diện về cách thiết kế và tích hợp hệ thống phân quyền RBAC bằng cách khởi tạo và kết nối dependencies thủ công. Việc tuân thủ các quy tắc và thực hành tốt nhất sẽ giúp xây dựng một hệ thống mạnh mẽ, dễ bảo trì và mở rộng, ngay cả khi không sử dụng DI framework.
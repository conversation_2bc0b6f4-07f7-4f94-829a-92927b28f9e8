meta {
  name: Login
  type: http
  seq: 2
}

post {
  url: {{api_url}}/api/v1/auth/login
  body: json
  auth: inherit
}

body:json {
  {
    "email": "<EMAIL>",
    "password": "Password123!"
  }
}

docs {
  title: "User Login"
  desc: "Authenticate a user and receive access and refresh tokens"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Đăng nhập thành công",
      "success": true,
      "path": "/api/v1/auth/login",
      "timestamp": "2025-05-25T20:45:27+07:00"
    },
    "data": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "access_token_expires_in": 3600,
      "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token_expires_in": 604800,
      "token_type": "Bearer",
      "user_id": 12345,
      "email": "<EMAIL>",
      "tenant_id": 1
    }
  }
}

response:401 {
  {
    "status": {
      "code": 401,
      "message": "Email hoặc mật khẩu không đúng",
      "success": false,
      "error_code": "INVALID_CREDENTIALS",
      "path": "/api/v1/auth/login",
      "timestamp": "2025-05-25T20:45:27+07:00"
    },
    "data": null
  }
}

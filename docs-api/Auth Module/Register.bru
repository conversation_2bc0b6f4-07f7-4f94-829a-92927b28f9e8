meta {
  name: Register
  type: http
  seq: 1
}

post {
  url: {{api_url}}/api/v1/auth/register
  body: json
  auth: inherit
}

body:json {
  {
    "email": "<EMAIL>",
    "password": "Password123!",
    "firstName": "<PERSON>",
    "lastName": "<PERSON><PERSON>",
    "username": "johndo<PERSON>"
  }
}

docs {
  title: "User Registration"
  desc: "Register a new user account with email and password"
}

meta {
  name: DeletePermission
  type: http
  seq: 5
}

delete {
  url: {{api_url}}/api/v1/rbac/permissions/{{permission_id}}
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "<PERSON><PERSON><PERSON> quyền"
  desc: "<PERSON><PERSON><PERSON> một quyền khỏi hệ thống"
}

response {
  {
    "status": {
      "code": 200,
      "message": "<PERSON>óa quyền thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/permissions/1",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": null,
    "meta": null
  }
}

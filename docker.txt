  # Database
  mysql:
    image: mysql:8.0
    container_name: wn-mysql-v2
    restart: always
    profiles: ["core"]
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: blog_v4
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./infrastructure/docker/mysql/mysql-custom.cnf:/etc/mysql/conf.d/custom.cnf
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
      interval: 5s
      timeout: 5s
      retries: 10
    networks:
      - wn-network
    command: ""

  # Minio - S3 compatible storage
  minio:
    image: minio/minio:latest
    container_name: wn-minio
    restart: always
    profiles: ["core"]
    environment:
      MINIO_ROOT_USER: minio
      MINIO_ROOT_PASSWORD: minio123
    ports:
      - "9071:9000"
      - "9072:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 5s
      timeout: 5s
      retries: 10
    networks:
      - wn-network

  # Redis
  redis:
    image: redis:7.0-alpine
    container_name: wn-redis
    restart: always
    profiles: ["core"]
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 10
    networks:
      - wn-network



  # Traefik - Alternative API Gateway
  traefik:
    image: traefik:v2.10
    container_name: wn-traefik
    profiles: ["traefik", "core"]
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080" # Traefik dashboard
    volumes:
      - ./infrastructure/traefik/traefik.yaml:/etc/traefik/traefik.yaml
      - ./infrastructure/traefik/providers:/etc/traefik/providers
      - ./infrastructure/traefik/logs:/var/log/traefik
    networks:
      - wn-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`traefik.wn-api.local`)"
      - "traefik.http.routers.dashboard.service=api@internal"
      - "traefik.http.routers.dashboard.entrypoints=web"

  # MailCatcher - Email testing
  mailcatcher:
    image: schickling/mailcatcher
    container_name: wn-mailcatcher
    restart: always
    profiles: ["core"]
    ports:
      - "1025:1025" # SMTP server
      - "1080:1080" # Web UI
    networks:
      - wn-network

  # Jaeger
  jaeger:
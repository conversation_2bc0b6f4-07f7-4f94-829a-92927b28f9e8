# Hướng dẫn Docker

File `docker-compose.yml` chứa các dịch vụ sau:

1. **API service** (`api`):
   - Build từ Dockerfile trong thư mục hiện tại
   - Container name: wnapi
   - Port: 8080
   - Volumes:
     - ./logs:/app/logs
     - ./config:/app/config
     - ./projects:/app/projects
   - <PERSON><PERSON> thuộc vào dịch vụ MySQL
   - Command: ["--project=sample"]

2. **MySQL database** (`mysql`):
   - Image: mysql:8.0
   - Container name: wnapi-mysql
   - Environment:
     - MYSQL_ROOT_PASSWORD: root
     - MYSQL_DATABASE: wnapi
     - MYSQL_USER: wnapi
     - MYSQL_PASSWORD: password
   - Port: 3307:3306
   - Volume: mysql-data:/var/lib/mysql
   - Command: --default-authentication-plugin=mysql_native_password

3. **Adminer** (`adminer`):
   - Image: adminer
   - Container name: wnapi-adminer
   - Port: 8081:8080
   - <PERSON><PERSON> thuộc vào dịch vụ MySQL

## Mạng
- Tất cả các dịch vụ sử dụng mạng `wnapi-network` với driver là bridge

## Volumes
- `mysql-data`: lưu trữ dữ liệu MySQL

## Cách sử dụng
```bash
# Khởi động các dịch vụ
docker compose up -d

# Kiểm tra trạng thái các dịch vụ
docker compose ps

# Xem logs
docker compose logs -f

# Dừng các dịch vụ
docker compose down
``` 
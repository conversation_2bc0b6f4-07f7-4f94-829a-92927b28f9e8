package site

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"

	"github.com/webnew/wn-backend-v2/modules/site/api"
	"github.com/webnew/wn-backend-v2/modules/site/configs"
	"github.com/webnew/wn-backend-v2/modules/site/repository/mysql"
	"github.com/webnew/wn-backend-v2/modules/site/service"
	"github.com/webnew/wn-backend-v2/modules/site/tracing"
	"github.com/webnew/wn-backend-v2/pkg/auth"
	"github.com/webnew/wn-backend-v2/pkg/middleware"
)

// Module đại diện cho module site
type Module struct {
	DB                *sqlx.DB
	Config            *configs.Config
	server            *http.Server
	router            *gin.Engine
	websiteService    service.WebsiteService
	pageService       service.PageService
	themeService      service.ThemeService
	templateService   service.TemplateService
	permissionService middleware.PermissionService
	jwtService        *auth.JWTService
}

// NewModule tạo một instance mới của module site
func NewModule(db *sqlx.DB) *Module {
	return &Module{
		DB: db,
	}
}

// NewModuleWithConfig tạo một instance mới của module site với cấu hình
func NewModuleWithConfig(cfg *configs.Config) *Module {
	// Thiết lập kết nối database
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true",
		cfg.DB.Username,
		cfg.DB.Password,
		cfg.DB.Host,
		cfg.DB.Port,
		cfg.DB.Database,
	)

	db, err := sqlx.Connect("mysql", dsn)
	if err != nil {
		log.Fatalf("Không thể kết nối đến database: %v", err)
	}

	// Thiết lập cấu hình connection pool
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	// Khởi tạo router
	router := gin.Default()

	router.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	// Khởi tạo tracing nếu được bật
	if cfg.Tracing != nil && cfg.Tracing.Enabled {
		log.Printf("Initializing tracing for site-service with exporter type: %s", cfg.Tracing.ExporterType)

		// Khởi tạo tracing
		err := tracing.InitTracing(cfg.Tracing)
		if err != nil {
			log.Printf("Warning: failed to initialize tracing: %v", err)
		} else {
			// Đăng ký middleware tracing
			tracingMiddleware := tracing.NewTracingMiddleware(cfg.Tracing)
			router.Use(tracingMiddleware.Middleware())
			log.Println("Tracing middleware registered")
		}
	}

	return &Module{
		DB:     db,
		Config: cfg,
		router: router,
	}
}

// RegisterRoutes đăng ký tất cả routes cho module site
func (m *Module) RegisterRoutes(router *gin.Engine) {
	// Tạo repositories
	websiteRepo := mysql.NewWebsiteRepository(m.DB)
	pageRepo := mysql.NewPageRepository(m.DB)
	themeRepo := mysql.NewThemeRepository(m.DB)
	templateRepo := mysql.NewTemplateRepository(m.DB)

	// Tạo services
	websiteService := service.NewWebsiteService(websiteRepo)
	pageService := service.NewPageService(pageRepo, websiteRepo)
	themeService := service.NewThemeService(themeRepo)
	templateService := service.NewTemplateService(templateRepo, themeRepo)
	permissionService := service.NewMockPermissionService()

	// Tạo JWT service
	jwtConfig := auth.JWTConfig{
		AccessSigningKey:       m.Config.JWT.AccessSigningKey,
		RefreshSigningKey:      m.Config.JWT.RefreshSigningKey,
		AccessTokenExpiration:  m.Config.JWT.AccessTokenDuration,
		RefreshTokenExpiration: m.Config.JWT.RefreshTokenDuration,
		Issuer:                 m.Config.JWT.Issuer,
	}
	jwtService := auth.NewJWTService(jwtConfig)
	m.jwtService = jwtService

	// Lưu services trong module để sử dụng bên ngoài nếu cần
	m.websiteService = websiteService
	m.pageService = pageService
	m.themeService = themeService
	m.templateService = templateService
	m.permissionService = permissionService

	// Đăng ký routes
	api.RegisterRoutes(router, websiteService, pageService, themeService, templateService, permissionService, jwtService)
}

// Start khởi động server của module site
func (m *Module) Start() error {
	// Nếu router chưa được thiết lập, sử dụng router từ module
	if m.router == nil {
		m.router = gin.Default()
	}

	// Đăng ký routes
	m.RegisterRoutes(m.router)

	// Tạo HTTP server
	m.server = &http.Server{
		Addr:    fmt.Sprintf("%s:%d", m.Config.Server.Host, m.Config.Server.Port),
		Handler: m.router,
	}

	// Khởi động server trong goroutine
	go func() {
		log.Printf("Khởi động site service tại %s:%d", m.Config.Server.Host, m.Config.Server.Port)
		if err := m.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Không thể khởi động server: %v", err)
		}
	}()

	return nil
}

// Stop dừng server của module site một cách nhẹ nhàng
func (m *Module) Stop() error {
	if m.server == nil {
		return nil
	}

	// Tạo context với timeout để shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Shutdown server
	if err := m.server.Shutdown(ctx); err != nil {
		return fmt.Errorf("server shutdown thất bại: %w", err)
	}

	// Đóng kết nối database
	if m.DB != nil {
		if err := m.DB.Close(); err != nil {
			return fmt.Errorf("đóng kết nối database thất bại: %w", err)
		}
	}

	// Dừng tracing
	if m.Config != nil && m.Config.Tracing != nil && m.Config.Tracing.Enabled {
		log.Println("Shutting down tracer...")
		tracing.Shutdown()
	}

	log.Println("Site service đã dừng một cách an toàn")
	return nil
}

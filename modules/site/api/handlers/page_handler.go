package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/webnew/wn-backend-v2/modules/site/dto/request"
	"github.com/webnew/wn-backend-v2/modules/site/service"
)

// PageHandler xử lý các HTTP requests liên quan đến page
type PageHandler struct {
	pageService service.PageService
}

// NewPageHandler tạo một instance mới của PageHandler
func NewPageHandler(pageService service.PageService) *PageHandler {
	return &PageHandler{
		pageService: pageService,
	}
}

// CreatePage xử lý request tạo page mới
func (h *PageHandler) CreatePage(c *gin.Context) {
	// Lấy website ID từ URL path
	websiteID, err := getWebsiteIDParam(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Parse request
	var req request.CreatePageRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Gọi service để tạo page
	page, err := h.pageService.CreatePage(c.Request.Context(), websiteID, req)
	if err != nil {
		handleDBError(c, err)
		return
	}

	// Trả về response thành công
	apiSuccess(c, http.StatusCreated, "Trang đã được tạo thành công", page)
}

// GetPage xử lý request lấy thông tin page theo ID
func (h *PageHandler) GetPage(c *gin.Context) {
	// Lấy website ID từ URL path
	websiteID, err := getWebsiteIDParam(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Lấy page ID từ URL path
	pageID, err := getIDParam(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Gọi service để lấy thông tin page
	page, err := h.pageService.GetPage(c.Request.Context(), websiteID, pageID)
	if err != nil {
		handleDBError(c, err)
		return
	}

	// Trả về response thành công
	apiSuccess(c, http.StatusOK, "Lấy thông tin trang thành công", page)
}

// GetPageBySlug xử lý request lấy thông tin page theo slug
func (h *PageHandler) GetPageBySlug(c *gin.Context) {
	// Lấy website ID từ URL path
	websiteID, err := getWebsiteIDParam(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Lấy slug từ URL path
	slug := c.Param("slug")
	if slug == "" {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, "Slug không được để trống")
		return
	}

	// Gọi service để lấy thông tin page
	page, err := h.pageService.GetPageBySlug(c.Request.Context(), websiteID, slug)
	if err != nil {
		handleDBError(c, err)
		return
	}

	// Trả về response thành công
	apiSuccess(c, http.StatusOK, "Lấy thông tin trang theo slug thành công", page)
}

// GetHomepage xử lý request lấy trang chủ của website
func (h *PageHandler) GetHomepage(c *gin.Context) {
	// Lấy website ID từ URL path
	websiteID, err := getWebsiteIDParam(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Gọi service để lấy thông tin trang chủ
	page, err := h.pageService.GetHomepage(c.Request.Context(), websiteID)
	if err != nil {
		handleDBError(c, err)
		return
	}

	// Trả về response thành công
	apiSuccess(c, http.StatusOK, "Lấy thông tin trang chủ thành công", page)
}

// UpdatePage xử lý request cập nhật page
func (h *PageHandler) UpdatePage(c *gin.Context) {
	// Lấy website ID từ URL path
	websiteID, err := getWebsiteIDParam(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Lấy page ID từ URL path
	pageID, err := getIDParam(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Parse request
	var req request.UpdatePageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Gọi service để cập nhật page
	page, err := h.pageService.UpdatePage(c.Request.Context(), websiteID, pageID, req)
	if err != nil {
		handleDBError(c, err)
		return
	}

	// Trả về response thành công
	apiSuccess(c, http.StatusOK, "Cập nhật trang thành công", page)
}

// DeletePage xử lý request xóa page
func (h *PageHandler) DeletePage(c *gin.Context) {
	// Lấy website ID từ URL path
	websiteID, err := getWebsiteIDParam(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Lấy page ID từ URL path
	pageID, err := getIDParam(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Gọi service để xóa page
	err = h.pageService.DeletePage(c.Request.Context(), websiteID, pageID)
	if err != nil {
		handleDBError(c, err)
		return
	}

	// Trả về response thành công
	apiSuccess(c, http.StatusOK, "Trang đã được xóa thành công", gin.H{"message": "Trang đã được xóa thành công"})
}

// ListPages xử lý request liệt kê danh sách page
func (h *PageHandler) ListPages(c *gin.Context) {
	// Lấy website ID từ URL path
	websiteID, err := getWebsiteIDParam(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Parse query parameters
	var req request.ListPageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Gọi service để lấy danh sách page
	result, err := h.pageService.ListPages(c.Request.Context(), websiteID, req)
	if err != nil {
		handleDBError(c, err)
		return
	}

	// Trả về response thành công
	meta := gin.H{
		"next_cursor": result.NextCursor,
		"has_more":    result.HasMore,
	}
	apiSuccessWithMeta(c, http.StatusOK, "Lấy danh sách trang thành công", result.Pages, meta)
}

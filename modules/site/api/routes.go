package api

import (
	"github.com/gin-gonic/gin"

	"github.com/webnew/wn-backend-v2/modules/site/api/handlers"
	"github.com/webnew/wn-backend-v2/modules/site/service"
	"github.com/webnew/wn-backend-v2/pkg/auth"
	"github.com/webnew/wn-backend-v2/pkg/middleware"
)

// RegisterRoutes đăng ký tất cả routes cho module site
func RegisterRoutes(router *gin.Engine, websiteService service.WebsiteService, pageService service.PageService, themeService service.ThemeService, templateService service.TemplateService, permService middleware.PermissionService, jwtService *auth.JWTService) {
	// Tạo API group
	api := router.Group("/api/v1/site")

	// Áp dụng JWT middleware cho tất cả routes
	api.Use(jwtService.JWTAuthMiddleware())

	// Tạo các handlers
	websiteHandler := handlers.NewWebsiteHandler(websiteService)
	pageHandler := handlers.NewPageHandler(pageService)
	themeHandler := handlers.NewThemeHandler(themeService)
	templateHandler := handlers.NewTemplateHandler(templateService)

	// Website routes
	websites := api.Group("/websites")
	{
		websites.GET("", middleware.RequirePermission(permService, "site.websites.read"), websiteHandler.ListWebsites)
		websites.POST("", middleware.RequirePermission(permService, "site.websites.create"), websiteHandler.CreateWebsite)
		websites.GET("/:id", middleware.RequirePermission(permService, "site.websites.read"), websiteHandler.GetWebsite)
		websites.PUT("/:id", middleware.RequirePermission(permService, "site.websites.update"), websiteHandler.UpdateWebsite)
		websites.DELETE("/:id", middleware.RequirePermission(permService, "site.websites.delete"), websiteHandler.DeleteWebsite)
	}

	// Page routes - Sử dụng path riêng biệt để tránh xung đột
	websitePages := api.Group("/website-pages/:website_id")
	{
		websitePages.GET("", middleware.RequirePermission(permService, "site.pages.read"), pageHandler.ListPages)
		websitePages.POST("", middleware.RequirePermission(permService, "site.pages.create"), pageHandler.CreatePage)
		websitePages.GET("/page/:id", middleware.RequirePermission(permService, "site.pages.read"), pageHandler.GetPage)
		websitePages.PUT("/page/:id", middleware.RequirePermission(permService, "site.pages.update"), pageHandler.UpdatePage)
		websitePages.DELETE("/page/:id", middleware.RequirePermission(permService, "site.pages.delete"), pageHandler.DeletePage)
		websitePages.GET("/homepage", middleware.RequirePermission(permService, "site.pages.read"), pageHandler.GetHomepage)
		websitePages.GET("/by-slug/:slug", middleware.RequirePermission(permService, "site.pages.read"), pageHandler.GetPageBySlug)
	}

	// Theme routes
	themes := api.Group("/themes")
	{
		themes.GET("", middleware.RequirePermission(permService, "site.themes.read"), themeHandler.ListThemes)
		themes.POST("", middleware.RequirePermission(permService, "site.themes.create"), themeHandler.CreateTheme)
		themes.GET("/:id", middleware.RequirePermission(permService, "site.themes.read"), themeHandler.GetTheme)
		themes.PUT("/:id", middleware.RequirePermission(permService, "site.themes.update"), themeHandler.UpdateTheme)
		themes.DELETE("/:id", middleware.RequirePermission(permService, "site.themes.delete"), themeHandler.DeleteTheme)
		themes.GET("/public", middleware.RequirePermission(permService, "site.themes.read"), themeHandler.ListPublicThemes)
	}

	// Template routes
	templates := api.Group("/theme-templates/:theme_id")
	{
		templates.GET("", middleware.RequirePermission(permService, "site.templates.read"), templateHandler.ListTemplates)
		templates.POST("", middleware.RequirePermission(permService, "site.templates.create"), templateHandler.CreateTemplate)
		templates.GET("/template/:id", middleware.RequirePermission(permService, "site.templates.read"), templateHandler.GetTemplate)
		templates.PUT("/template/:id", middleware.RequirePermission(permService, "site.templates.update"), templateHandler.UpdateTemplate)
		templates.DELETE("/template/:id", middleware.RequirePermission(permService, "site.templates.delete"), templateHandler.DeleteTemplate)
		templates.GET("/by-type/:type", middleware.RequirePermission(permService, "site.templates.read"), templateHandler.ListTemplatesByType)
	}
}

package request

// UpdateWebsiteRequest đại diện cho yêu cầu cập nhật website
type UpdateWebsiteRequest struct {
	Name         *string `json:"name" binding:"omitempty,max=100"`
	Subdomain    *string `json:"subdomain" binding:"omitempty,max=50"`
	CustomDomain *string `json:"custom_domain" binding:"omitempty,max=255"`
	Description  *string `json:"description" binding:"omitempty"`
	Status       *string `json:"status" binding:"omitempty,oneof=draft active inactive maintenance building deleted"`
	ThemeID      *int    `json:"theme_id" binding:"omitempty"`
}

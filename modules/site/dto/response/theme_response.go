package response

import (
	"time"
)

// ThemeResponse đại diện cho response theme
type ThemeResponse struct {
	ID           int                `json:"id"`
	Name         string             `json:"name"`
	Description  *string            `json:"description,omitempty"`
	ThumbnailURL *string            `json:"thumbnail_url,omitempty"`
	IsPublic     bool               `json:"is_public"`
	CreatedBy    *int               `json:"created_by,omitempty"`
	CreatedAt    time.Time          `json:"created_at"`
	UpdatedAt    time.Time          `json:"updated_at"`
	Templates    []TemplateResponse `json:"templates,omitempty"`
}

// ThemeListResponse đại diện cho response danh sách theme
type ThemeListResponse struct {
	Themes     []ThemeResponse `json:"themes"`
	NextCursor string          `json:"next_cursor"`
	Has<PERSON><PERSON>    bool            `json:"has_more"`
}

package response

import (
	"time"

	"github.com/webnew/wn-backend-v2/modules/site/models"
)

// TemplateResponse đại diện cho response template
type TemplateResponse struct {
	ID             int                   `json:"id"`
	ThemeID        int                   `json:"theme_id"`
	Name           string                `json:"name"`
	Type           string                `json:"type"`
	HTMLStructure  string                `json:"html_structure"`
	CSS            *string               `json:"css,omitempty"`
	DefaultContent models.DefaultContent `json:"default_content,omitempty"`
	CreatedAt      time.Time             `json:"created_at"`
	UpdatedAt      time.Time             `json:"updated_at"`
	Theme          *ThemeResponse        `json:"theme,omitempty"`
}

// TemplateListResponse đại diện cho response danh sách template
type TemplateListResponse struct {
	Templates  []TemplateResponse `json:"templates"`
	NextCursor string             `json:"next_cursor"`
	Has<PERSON><PERSON>    bool               `json:"has_more"`
}

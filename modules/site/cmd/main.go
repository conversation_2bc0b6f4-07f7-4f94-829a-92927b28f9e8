package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	_ "github.com/go-sql-driver/mysql"
	"github.com/webnew/wn-backend-v2/modules/site"
	"github.com/webnew/wn-backend-v2/modules/site/configs"
)

func main() {
	// Load config using the configs package
	cfg, err := configs.LoadConfig()
	if err != nil {
		log.Fatalf("Error loading config: %v", err)
	}

	// Create and start module
	module := site.NewModuleWithConfig(cfg)
	if err := module.Start(); err != nil {
		log.Fatalf("Error starting module: %v", err)
	}

	// Wait for termination signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down site service...")

	// Stop module
	if err := module.Stop(); err != nil {
		log.Fatalf("Error stopping module: %v", err)
	}

	log.Println("Site service exited")
}

package tracing

import (
	"context"
	"fmt"
	"time"

	"github.com/webnew/wn-backend-v2/modules/site/configs"
)

// TraceService wraps a service method call with tracing
func TraceService(ctx context.Context, cfg *configs.TracingConfig, serviceName, methodName string, f func(context.Context) error) error {
	if cfg == nil || !cfg.Enabled {
		return f(ctx)
	}

	operationName := fmt.Sprintf("%s.%s", serviceName, methodName)
	ctx, span := StartSpan(ctx, cfg, operationName)
	defer EndSpan(span)

	if span != nil {
		AddAttribute(span, "service", serviceName)
		AddAttribute(span, "method", methodName)
	}

	startTime := time.Now()
	err := f(ctx)
	duration := time.Since(startTime)

	if span != nil {
		AddAttribute(span, "duration_ms", duration.Milliseconds())
	}

	if err != nil && span != nil {
		RecordError(span, err)
	}

	return err
}

// TraceServiceWithResult wraps a service method call with tracing and returns a result
func TraceServiceWithResult[T any](ctx context.Context, cfg *configs.TracingConfig, serviceName, methodName string, f func(context.Context) (T, error)) (T, error) {
	if cfg == nil || !cfg.Enabled {
		return f(ctx)
	}

	operationName := fmt.Sprintf("%s.%s", serviceName, methodName)
	ctx, span := StartSpan(ctx, cfg, operationName)
	defer EndSpan(span)

	if span != nil {
		AddAttribute(span, "service", serviceName)
		AddAttribute(span, "method", methodName)
	}

	startTime := time.Now()
	result, err := f(ctx)
	duration := time.Since(startTime)

	if span != nil {
		AddAttribute(span, "duration_ms", duration.Milliseconds())
	}

	if err != nil && span != nil {
		RecordError(span, err)
	}

	return result, err
}

// TraceRepository wraps a repository method call with tracing
func TraceRepository(ctx context.Context, cfg *configs.TracingConfig, repoName, methodName string, f func(context.Context) error) error {
	if cfg == nil || !cfg.Enabled {
		return f(ctx)
	}

	operationName := fmt.Sprintf("repository.%s.%s", repoName, methodName)
	ctx, span := StartSpan(ctx, cfg, operationName)
	defer EndSpan(span)

	if span != nil {
		AddAttribute(span, "repository", repoName)
		AddAttribute(span, "method", methodName)
	}

	startTime := time.Now()
	err := f(ctx)
	duration := time.Since(startTime)

	if span != nil {
		AddAttribute(span, "duration_ms", duration.Milliseconds())
		AddAttribute(span, "db.operation", methodName)
	}

	if err != nil && span != nil {
		RecordError(span, err)
	}

	return err
}

// TraceRepositoryWithResult wraps a repository method call with tracing and returns a result
func TraceRepositoryWithResult[T any](ctx context.Context, cfg *configs.TracingConfig, repoName, methodName string, f func(context.Context) (T, error)) (T, error) {
	if cfg == nil || !cfg.Enabled {
		return f(ctx)
	}

	operationName := fmt.Sprintf("repository.%s.%s", repoName, methodName)
	ctx, span := StartSpan(ctx, cfg, operationName)
	defer EndSpan(span)

	if span != nil {
		AddAttribute(span, "repository", repoName)
		AddAttribute(span, "method", methodName)
	}

	startTime := time.Now()
	result, err := f(ctx)
	duration := time.Since(startTime)

	if span != nil {
		AddAttribute(span, "duration_ms", duration.Milliseconds())
		AddAttribute(span, "db.operation", methodName)
	}

	if err != nil && span != nil {
		RecordError(span, err)
	}

	return result, err
}

// TraceSiteOperation wraps a site operation with tracing
func TraceSiteOperation(ctx context.Context, cfg *configs.TracingConfig, operation string, siteID int, f func(context.Context) error) error {
	if cfg == nil || !cfg.Enabled {
		return f(ctx)
	}

	operationName := fmt.Sprintf("site.%s", operation)
	ctx, span := StartSpan(ctx, cfg, operationName)
	defer EndSpan(span)

	if span != nil {
		AddAttribute(span, "site.operation", operation)
		AddAttribute(span, "site.id", siteID)
	}

	startTime := time.Now()
	err := f(ctx)
	duration := time.Since(startTime)

	if span != nil {
		AddAttribute(span, "duration_ms", duration.Milliseconds())
	}

	if err != nil && span != nil {
		RecordError(span, err)
	}

	return err
}

// TraceSiteOperationWithResult wraps a site operation with tracing and returns a result
func TraceSiteOperationWithResult[T any](ctx context.Context, cfg *configs.TracingConfig, operation string, siteID int, f func(context.Context) (T, error)) (T, error) {
	if cfg == nil || !cfg.Enabled {
		return f(ctx)
	}

	operationName := fmt.Sprintf("site.%s", operation)
	ctx, span := StartSpan(ctx, cfg, operationName)
	defer EndSpan(span)

	if span != nil {
		AddAttribute(span, "site.operation", operation)
		AddAttribute(span, "site.id", siteID)
	}

	startTime := time.Now()
	result, err := f(ctx)
	duration := time.Since(startTime)

	if span != nil {
		AddAttribute(span, "duration_ms", duration.Milliseconds())
	}

	if err != nil && span != nil {
		RecordError(span, err)
	}

	return result, err
}

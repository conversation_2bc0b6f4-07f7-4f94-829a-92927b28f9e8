package tracing

import (
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/webnew/wn-backend-v2/modules/site/configs"
)

// TracingMiddleware is a middleware for tracing HTTP requests
type TracingMiddleware struct {
	config *configs.TracingConfig
}

// NewTracingMiddleware creates a new tracing middleware
func NewTracingMiddleware(config *configs.TracingConfig) *TracingMiddleware {
	return &TracingMiddleware{
		config: config,
	}
}

// Middleware returns a gin middleware function
func (m *TracingMiddleware) Middleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if m.config == nil || !m.config.Enabled {
			c.Next()
			return
		}

		// Start a new span
		operationName := "http.request"
		ctx, span := StartSpan(c.Request.Context(), m.config, operationName)
		startTime := time.Now()

		// Add HTTP request attributes
		if span != nil {
			AddAttribute(span, "http.method", c.Request.Method)
			AddAttribute(span, "http.url", c.Request.URL.String())
			AddAttribute(span, "http.path", c.FullPath())
			AddAttribute(span, "http.user_agent", c.Request.UserAgent())
			
			// Add site-specific attributes if available
			if siteID := c.Param("id"); siteID != "" {
				if id, err := strconv.Atoi(siteID); err == nil {
					AddAttribute(span, "site.id", id)
				}
			}
			
			if pageID := c.Param("page_id"); pageID != "" {
				if id, err := strconv.Atoi(pageID); err == nil {
					AddAttribute(span, "site.page_id", id)
				}
			}
			
			if themeID := c.Param("theme_id"); themeID != "" {
				if id, err := strconv.Atoi(themeID); err == nil {
					AddAttribute(span, "site.theme_id", id)
				}
			}
			
			// Extract operation type from path
			path := c.FullPath()
			if path != "" {
				var operation string
				switch {
				case contains(path, "/websites"):
					operation = "website_management"
				case contains(path, "/pages"):
					operation = "page_management"
				case contains(path, "/themes"):
					operation = "theme_management"
				case contains(path, "/templates"):
					operation = "template_management"
				case contains(path, "/settings"):
					operation = "settings_management"
				case contains(path, "/navigation"):
					operation = "navigation_management"
				case contains(path, "/assets"):
					operation = "asset_management"
				default:
					operation = "other"
				}
				AddAttribute(span, "site.operation", operation)
			}
		}

		// Update the context
		c.Request = c.Request.WithContext(ctx)

		// Process the request
		c.Next()

		// Add response attributes
		if span != nil {
			statusCode := c.Writer.Status()
			AddAttribute(span, "http.status_code", statusCode)
			
			// Record duration
			duration := time.Since(startTime)
			AddAttribute(span, "duration_ms", duration.Milliseconds())
			
			// Record error if status code is 4xx or 5xx
			if statusCode >= 400 {
				AddAttribute(span, "error", true)
				AddAttribute(span, "error.message", fmt.Sprintf("HTTP status code: %d", statusCode))
			}
		}
		
		// End the span
		EndSpan(span)
	}
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return s != "" && substr != "" && s != substr && len(s) >= len(substr) && s[0:len(substr)] == substr
}

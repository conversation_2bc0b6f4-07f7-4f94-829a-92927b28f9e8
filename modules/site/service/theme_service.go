package service

import (
	"context"
	"errors"
	"time"

	"github.com/webnew/wn-backend-v2/modules/site/dto/request"
	"github.com/webnew/wn-backend-v2/modules/site/models"
	"github.com/webnew/wn-backend-v2/modules/site/repository"
)

// Định nghĩa các lỗi
var (
	ErrThemeNotFound = errors.New("theme không tồn tại")
)

// ThemeService định nghĩa service xử lý theme
type ThemeService interface {
	GetThemes(limit int, cursor string) ([]models.Theme, string, bool, error)
	GetPublicThemes(limit int, cursor string) ([]models.Theme, string, bool, error)
	GetThemeByID(id int) (*models.Theme, error)
	CreateTheme(theme *models.Theme) (*models.Theme, error)
	UpdateTheme(id int, theme *models.Theme) (*models.Theme, error)
	DeleteTheme(id int) error
}

type themeService struct {
	themeRepo repository.ThemeRepository
}

// NewThemeService tạo mới theme service
func NewThemeService(themeRepo repository.ThemeRepository) ThemeService {
	return &themeService{
		themeRepo: themeRepo,
	}
}

// GetThemes lấy danh sách themes với phân trang
func (s *themeService) GetThemes(limit int, cursor string) ([]models.Theme, string, bool, error) {
	req := request.ListThemeRequest{
		Limit:  limit,
		Cursor: cursor,
	}

	themes, nextCursor, hasMore, err := s.themeRepo.List(context.Background(), req)
	if err != nil {
		return nil, "", false, err
	}

	// Chuyển đổi []*models.Theme thành []models.Theme
	result := make([]models.Theme, len(themes))
	for i, theme := range themes {
		result[i] = *theme
	}

	return result, nextCursor, hasMore, nil
}

// GetPublicThemes lấy danh sách public themes với phân trang
func (s *themeService) GetPublicThemes(limit int, cursor string) ([]models.Theme, string, bool, error) {
	req := request.ListThemeRequest{
		Limit:  limit,
		Cursor: cursor,
	}

	themes, nextCursor, hasMore, err := s.themeRepo.ListPublic(context.Background(), req)
	if err != nil {
		return nil, "", false, err
	}

	// Chuyển đổi []*models.Theme thành []models.Theme
	result := make([]models.Theme, len(themes))
	for i, theme := range themes {
		result[i] = *theme
	}

	return result, nextCursor, hasMore, nil
}

// GetThemeByID lấy theme theo ID
func (s *themeService) GetThemeByID(id int) (*models.Theme, error) {
	theme, err := s.themeRepo.GetByID(context.Background(), id)
	if err != nil {
		return nil, err
	}
	if theme == nil {
		return nil, ErrThemeNotFound
	}
	return theme, nil
}

// CreateTheme tạo mới theme
func (s *themeService) CreateTheme(theme *models.Theme) (*models.Theme, error) {
	// Set thời gian
	now := time.Now()
	theme.CreatedAt = now
	theme.UpdatedAt = now

	// Lưu vào repository
	err := s.themeRepo.Create(context.Background(), theme)
	if err != nil {
		return nil, err
	}

	// Lấy theme mới tạo
	return s.GetThemeByID(theme.ThemeID)
}

// UpdateTheme cập nhật theme
func (s *themeService) UpdateTheme(id int, theme *models.Theme) (*models.Theme, error) {
	// Kiểm tra tồn tại
	existingTheme, err := s.GetThemeByID(id)
	if err != nil {
		return nil, err
	}

	// Cập nhật thông tin
	theme.ThemeID = id
	theme.CreatedAt = existingTheme.CreatedAt
	theme.UpdatedAt = time.Now()

	// Lưu vào repository
	err = s.themeRepo.Update(context.Background(), theme)
	if err != nil {
		return nil, err
	}

	// Lấy theme mới cập nhật
	return s.GetThemeByID(id)
}

// DeleteTheme xóa theme
func (s *themeService) DeleteTheme(id int) error {
	// Kiểm tra tồn tại
	_, err := s.GetThemeByID(id)
	if err != nil {
		return err
	}

	// Xóa theme
	return s.themeRepo.Delete(context.Background(), id)
}

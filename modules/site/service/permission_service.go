package service

import (
	"github.com/gin-gonic/gin"
	"github.com/webnew/wn-backend-v2/pkg/middleware"
)

// MockPermissionService is a temporary implementation of PermissionService
type MockPermissionService struct{}

// NewMockPermissionService creates a new instance of MockPermissionService
func NewMockPermissionService() middleware.PermissionService {
	return &MockPermissionService{}
}

// CheckPermission implements the PermissionService interface
// This is a temporary implementation that always returns true
func (s *MockPermissionService) CheckPermission(ctx *gin.Context, tenantID, userID uint, permission string) (bool, error) {
	// TODO: Implement actual permission checking or integrate with tenant module
	return true, nil
}

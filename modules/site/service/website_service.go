package service

import (
	"context"

	"github.com/webnew/wn-backend-v2/modules/site/dto/request"
	"github.com/webnew/wn-backend-v2/modules/site/dto/response"
	"github.com/webnew/wn-backend-v2/modules/site/models"
	"github.com/webnew/wn-backend-v2/modules/site/repository"
)

// WebsiteService định nghĩa interface cho business logic website
type WebsiteService interface {
	// Các thao tác CRUD cơ bản
	CreateWebsite(ctx context.Context, tenantID int, req request.CreateWebsiteRequest) (*response.WebsiteResponse, error)
	GetWebsite(ctx context.Context, tenantID, websiteID int) (*response.WebsiteResponse, error)
	GetWebsiteBySubdomain(ctx context.Context, subdomain string) (*response.WebsiteResponse, error)
	GetWebsiteByCustomDomain(ctx context.Context, customDomain string) (*response.WebsiteResponse, error)
	UpdateWebsite(ctx context.Context, tenantID, websiteID int, req request.UpdateWebsiteRequest) (*response.WebsiteResponse, error)
	DeleteWebsite(ctx context.Context, tenantID, websiteID int) error
	ListWebsites(ctx context.Context, tenantID int, req request.ListWebsiteRequest) (*response.WebsiteListResponse, error)
}

// websiteService triển khai WebsiteService interface
type websiteService struct {
	websiteRepo repository.WebsiteRepository
}

// NewWebsiteService tạo một instance mới của WebsiteService
func NewWebsiteService(websiteRepo repository.WebsiteRepository) WebsiteService {
	return &websiteService{
		websiteRepo: websiteRepo,
	}
}

// CreateWebsite tạo một website mới
func (s *websiteService) CreateWebsite(ctx context.Context, tenantID int, req request.CreateWebsiteRequest) (*response.WebsiteResponse, error) {
	// Tạo model từ request
	website := &models.Website{
		TenantID:     tenantID,
		Name:         req.Name,
		Subdomain:    req.Subdomain,
		CustomDomain: req.CustomDomain,
		Description:  req.Description,
		Status:       req.Status,
		ThemeID:      req.ThemeID,
	}

	// Lưu vào repository
	if err := s.websiteRepo.Create(ctx, website); err != nil {
		return nil, err
	}

	// Chuyển đổi sang response
	return s.convertToResponse(website), nil
}

// GetWebsite lấy thông tin website theo ID
func (s *websiteService) GetWebsite(ctx context.Context, tenantID, websiteID int) (*response.WebsiteResponse, error) {
	// Lấy từ repository
	website, err := s.websiteRepo.GetByID(ctx, tenantID, websiteID)
	if err != nil {
		return nil, err
	}

	// Chuyển đổi sang response
	return s.convertToResponse(website), nil
}

// GetWebsiteBySubdomain lấy thông tin website theo subdomain
func (s *websiteService) GetWebsiteBySubdomain(ctx context.Context, subdomain string) (*response.WebsiteResponse, error) {
	// Lấy từ repository
	website, err := s.websiteRepo.GetBySubdomain(ctx, subdomain)
	if err != nil {
		return nil, err
	}

	// Chuyển đổi sang response
	return s.convertToResponse(website), nil
}

// GetWebsiteByCustomDomain lấy thông tin website theo custom domain
func (s *websiteService) GetWebsiteByCustomDomain(ctx context.Context, customDomain string) (*response.WebsiteResponse, error) {
	// Lấy từ repository
	website, err := s.websiteRepo.GetByCustomDomain(ctx, customDomain)
	if err != nil {
		return nil, err
	}

	// Chuyển đổi sang response
	return s.convertToResponse(website), nil
}

// UpdateWebsite cập nhật thông tin website
func (s *websiteService) UpdateWebsite(ctx context.Context, tenantID, websiteID int, req request.UpdateWebsiteRequest) (*response.WebsiteResponse, error) {
	// Lấy website hiện tại
	existingWebsite, err := s.websiteRepo.GetByID(ctx, tenantID, websiteID)
	if err != nil {
		return nil, err
	}

	// Cập nhật các trường được gửi trong request
	if req.Name != nil {
		existingWebsite.Name = *req.Name
	}

	if req.Subdomain != nil {
		existingWebsite.Subdomain = req.Subdomain
	}

	if req.CustomDomain != nil {
		existingWebsite.CustomDomain = req.CustomDomain
	}

	if req.Description != nil {
		existingWebsite.Description = req.Description
	}

	if req.Status != nil {
		existingWebsite.Status = *req.Status
	}

	if req.ThemeID != nil {
		existingWebsite.ThemeID = req.ThemeID
	}

	// Lưu vào repository
	if err := s.websiteRepo.Update(ctx, existingWebsite); err != nil {
		return nil, err
	}

	// Chuyển đổi sang response
	return s.convertToResponse(existingWebsite), nil
}

// DeleteWebsite xóa website
func (s *websiteService) DeleteWebsite(ctx context.Context, tenantID, websiteID int) error {
	return s.websiteRepo.Delete(ctx, tenantID, websiteID)
}

// ListWebsites liệt kê danh sách website với phân trang
func (s *websiteService) ListWebsites(ctx context.Context, tenantID int, req request.ListWebsiteRequest) (*response.WebsiteListResponse, error) {
	// Lấy danh sách từ repository
	websites, nextCursor, hasMore, err := s.websiteRepo.List(ctx, tenantID, req)
	if err != nil {
		return nil, err
	}

	// Chuyển đổi sang response
	websiteResponses := make([]response.WebsiteResponse, 0, len(websites))
	for _, website := range websites {
		websiteResponses = append(websiteResponses, *s.convertToResponse(website))
	}

	return &response.WebsiteListResponse{
		Websites:   websiteResponses,
		NextCursor: nextCursor,
		HasMore:    hasMore,
	}, nil
}

// Helper functions

// convertToResponse chuyển đổi model sang response
func (s *websiteService) convertToResponse(website *models.Website) *response.WebsiteResponse {
	return &response.WebsiteResponse{
		ID:           website.WebsiteID,
		TenantID:     website.TenantID,
		Name:         website.Name,
		Subdomain:    website.Subdomain,
		CustomDomain: website.CustomDomain,
		Description:  website.Description,
		Status:       website.Status,
		ThemeID:      website.ThemeID,
		CreatedAt:    website.CreatedAt,
		UpdatedAt:    website.UpdatedAt,
		Theme:        nil, // Theme sẽ được điền nếu cần
	}
}

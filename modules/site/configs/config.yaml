server:
  host: "0.0.0.0"
  port: 9047

db:
  host: "localhost"
  port: 3307
  username: "root"
  password: "root"
  database: "blog_v4"

jwt:
  access_signing_key: "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
  refresh_signing_key: "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
  access_token_expiration: "24h"
  refresh_token_expiration: "720h"
  issuer: "wn-backend"

tracing:
  enabled: true
  service_name: "site-service"
  exporter_type: "jaeger"
  signoz:
    endpoint: "localhost:4317"
  jaeger:
    host: "localhost"
    port: "6831"
  sample_ratio: 1.0
package configs

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/spf13/viper"
)

// Config đại diện cho cấu hình module site
type Config struct {
	Server  ServerConfig   `yaml:"server" mapstructure:"server"`
	DB      DBConfig       `yaml:"db" mapstructure:"db"`
	JWT     JWTConfig      `yaml:"jwt" mapstructure:"jwt"`
	Tracing *TracingConfig `yaml:"tracing" mapstructure:"tracing"`
}

// JWTConfig đại diện cho cấu hình JWT
type JWTConfig struct {
	AccessSigningKey       string        `yaml:"access_signing_key" mapstructure:"access_signing_key"`
	RefreshSigningKey      string        `yaml:"refresh_signing_key" mapstructure:"refresh_signing_key"`
	AccessTokenExpiration  string        `yaml:"access_token_expiration" mapstructure:"access_token_expiration"`
	RefreshTokenExpiration string        `yaml:"refresh_token_expiration" mapstructure:"refresh_token_expiration"`
	AccessTokenDuration    time.Duration `yaml:"-" mapstructure:"-"` // Thời gian hết hạn đã chuyển đổi
	RefreshTokenDuration   time.Duration `yaml:"-" mapstructure:"-"` // Thời gian hết hạn đã chuyển đổi
	Issuer                 string        `yaml:"issuer" mapstructure:"issuer"`
}

// TracingConfig đại diện cho cấu hình tracing
type TracingConfig struct {
	Enabled      bool   `yaml:"enabled" mapstructure:"enabled"`
	ServiceName  string `yaml:"service_name" mapstructure:"service_name"`
	ExporterType string `yaml:"exporter_type" mapstructure:"exporter_type"`
	Signoz       struct {
		Endpoint string `yaml:"endpoint" mapstructure:"endpoint"`
	} `yaml:"signoz" mapstructure:"signoz"`
	Jaeger struct {
		Host string `yaml:"host" mapstructure:"host"`
		Port string `yaml:"port" mapstructure:"port"`
	} `yaml:"jaeger" mapstructure:"jaeger"`
	SampleRatio float64 `yaml:"sample_ratio" mapstructure:"sample_ratio"`
}

// ServerConfig đại diện cho cấu hình server của module site
type ServerConfig struct {
	Host string `yaml:"host" mapstructure:"host"`
	Port int    `yaml:"port" mapstructure:"port"`
}

// DBConfig đại diện cho cấu hình database của module site
type DBConfig struct {
	Host     string `yaml:"host" mapstructure:"host"`
	Port     int    `yaml:"port" mapstructure:"port"`
	Username string `yaml:"username" mapstructure:"username"`
	Password string `yaml:"password" mapstructure:"password"`
	Database string `yaml:"database" mapstructure:"database"`
}

// LoadConfig loads the configuration from config files
func LoadConfig() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("../configs")
	viper.AddConfigPath("../../configs")

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Override with environment variables if set
	if dbHost := os.Getenv("DB_HOST"); dbHost != "" {
		config.DB.Host = dbHost
	}

	if dbPort := os.Getenv("DB_PORT"); dbPort != "" {
		if port, err := strconv.Atoi(dbPort); err == nil {
			config.DB.Port = port
		}
	}

	if dbUser := os.Getenv("DB_USER"); dbUser != "" {
		config.DB.Username = dbUser
	}

	if dbPass := os.Getenv("DB_PASSWORD"); dbPass != "" {
		config.DB.Password = dbPass
	}

	if dbName := os.Getenv("DB_NAME"); dbName != "" {
		config.DB.Database = dbName
	}

	if httpPort := os.Getenv("HTTP_PORT"); httpPort != "" {
		if port, err := strconv.Atoi(httpPort); err == nil {
			config.Server.Port = port
		}
	}

	// Initialize tracing config if not set
	if config.Tracing == nil {
		config.Tracing = &TracingConfig{
			Enabled:      false,
			ServiceName:  "site-service",
			ExporterType: "signoz",
			SampleRatio:  1.0,
		}
	}

	// Handle tracing configuration from environment variables
	if tracingEnabled := os.Getenv("TRACING_ENABLED"); tracingEnabled != "" {
		config.Tracing.Enabled = tracingEnabled == "true"
	}

	if serviceName := os.Getenv("TRACING_SERVICE_NAME"); serviceName != "" {
		config.Tracing.ServiceName = serviceName
	}

	if exporterType := os.Getenv("TRACING_EXPORTER_TYPE"); exporterType != "" {
		config.Tracing.ExporterType = exporterType
	}

	if sampleRatio := os.Getenv("TRACING_SAMPLE_RATIO"); sampleRatio != "" {
		if ratio, err := strconv.ParseFloat(sampleRatio, 64); err == nil {
			config.Tracing.SampleRatio = ratio
		}
	}

	if signozEndpoint := os.Getenv("SIGNOZ_ENDPOINT"); signozEndpoint != "" {
		config.Tracing.Signoz.Endpoint = signozEndpoint
	}

	// Handle Jaeger agent host and port
	jaegerHost := os.Getenv("JAEGER_AGENT_HOST")
	jaegerPort := os.Getenv("JAEGER_AGENT_PORT")
	if jaegerHost != "" {
		config.Tracing.Jaeger.Host = jaegerHost
	}
	if jaegerPort != "" {
		config.Tracing.Jaeger.Port = jaegerPort
	}

	// Enable tracing if Jaeger or SigNoz is configured
	if (jaegerHost != "" && jaegerPort != "") || config.Tracing.Signoz.Endpoint != "" {
		config.Tracing.Enabled = true
	}

	// Handle JWT configuration from environment variables
	if jwtAccessKey := os.Getenv("JWT_ACCESS_SIGNING_KEY"); jwtAccessKey != "" {
		config.JWT.AccessSigningKey = jwtAccessKey
	}

	if jwtRefreshKey := os.Getenv("JWT_REFRESH_SIGNING_KEY"); jwtRefreshKey != "" {
		config.JWT.RefreshSigningKey = jwtRefreshKey
	}

	if jwtIssuer := os.Getenv("JWT_ISSUER"); jwtIssuer != "" {
		config.JWT.Issuer = jwtIssuer
	}

	if jwtAccessExpiry := os.Getenv("JWT_ACCESS_TOKEN_EXPIRATION"); jwtAccessExpiry != "" {
		config.JWT.AccessTokenExpiration = jwtAccessExpiry
	}

	if jwtRefreshExpiry := os.Getenv("JWT_REFRESH_TOKEN_EXPIRATION"); jwtRefreshExpiry != "" {
		config.JWT.RefreshTokenExpiration = jwtRefreshExpiry
	}

	// Parse JWT token durations
	if config.JWT.AccessTokenExpiration != "" {
		if duration, err := time.ParseDuration(config.JWT.AccessTokenExpiration); err == nil {
			config.JWT.AccessTokenDuration = duration
		} else {
			log.Printf("Warning: Could not parse JWT access token expiration: %v", err)
			config.JWT.AccessTokenDuration = 24 * time.Hour // Default to 24 hours
		}
	} else {
		config.JWT.AccessTokenDuration = 24 * time.Hour // Default to 24 hours
	}

	if config.JWT.RefreshTokenExpiration != "" {
		if duration, err := time.ParseDuration(config.JWT.RefreshTokenExpiration); err == nil {
			config.JWT.RefreshTokenDuration = duration
		} else {
			log.Printf("Warning: Could not parse JWT refresh token expiration: %v", err)
			config.JWT.RefreshTokenDuration = 720 * time.Hour // Default to 30 days
		}
	} else {
		config.JWT.RefreshTokenDuration = 720 * time.Hour // Default to 30 days
	}

	// Set default values if they are not set
	if config.DB.Host == "" {
		config.DB.Host = "mysql"
	}
	if config.DB.Port == 0 {
		config.DB.Port = 3306
	}
	if config.DB.Username == "" {
		config.DB.Username = "root"
	}
	if config.DB.Database == "" {
		config.DB.Database = "blog_v4"
	}
	if config.Server.Host == "" {
		config.Server.Host = "0.0.0.0"
	}
	if config.Server.Port == 0 {
		config.Server.Port = 9047
	}

	// Print config in Docker environment
	if os.Getenv("DOCKER_ENV") == "true" {
		PrintConfig(&config)
	}

	return &config, nil
}

// PrintConfig prints all configuration values in a formatted JSON
func PrintConfig(config *Config) {
	// Create a copy of the config with the password masked for security
	configCopy := *config
	configCopy.DB.Password = "********"

	// Marshal config to JSON for pretty printing
	configJSON, err := json.MarshalIndent(configCopy, "", "  ")
	if err != nil {
		fmt.Printf("Error marshaling config: %v\n", err)
		return
	}

	fmt.Printf("=== SITE MODULE CONFIGURATION ===\n%s\n==============================\n", string(configJSON))
}

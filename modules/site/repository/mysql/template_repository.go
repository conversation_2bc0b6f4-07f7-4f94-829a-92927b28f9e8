package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/webnew/wn-backend-v2/modules/site/dto/request"
	"github.com/webnew/wn-backend-v2/modules/site/models"
	"github.com/webnew/wn-backend-v2/modules/site/repository"
)

// TemplateRepository là MySQL implementation của TemplateRepository interface
type TemplateRepository struct {
	db *sqlx.DB
}

// NewTemplateRepository tạo mới instance của TemplateRepository
func NewTemplateRepository(db *sqlx.DB) repository.TemplateRepository {
	return &TemplateRepository{
		db: db,
	}
}

// Create thêm một template mới vào database
func (r *TemplateRepository) Create(ctx context.Context, template *models.Template) error {
	query := `
		INSERT INTO templates (
			theme_id, name, type, html_structure, css, default_content,
			created_at, updated_at
		) VALUES (
			:theme_id, :name, :type, :html_structure, :css, :default_content,
			:created_at, :updated_at
		)
	`

	result, err := r.db.NamedExecContext(ctx, query, template)
	if err != nil {
		return fmt.Errorf("không thể tạo template: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("không thể lấy ID của template vừa tạo: %w", err)
	}

	template.TemplateID = int(id)
	return nil
}

// GetByID lấy template theo ID
func (r *TemplateRepository) GetByID(ctx context.Context, themeID, templateID int) (*models.Template, error) {
	var query string
	var args []interface{}

	if themeID > 0 {
		query = `
			SELECT * FROM templates
			WHERE template_id = ? AND theme_id = ?
		`
		args = []interface{}{templateID, themeID}
	} else {
		query = `
			SELECT * FROM templates
			WHERE template_id = ?
		`
		args = []interface{}{templateID}
	}

	var template models.Template
	err := r.db.GetContext(ctx, &template, query, args...)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("không thể lấy template: %w", err)
	}

	return &template, nil
}

// Update cập nhật template
func (r *TemplateRepository) Update(ctx context.Context, template *models.Template) error {
	query := `
		UPDATE templates
		SET name = :name, 
			type = :type, 
			html_structure = :html_structure, 
			css = :css, 
			default_content = :default_content, 
			updated_at = :updated_at
		WHERE template_id = :template_id AND theme_id = :theme_id
	`

	result, err := r.db.NamedExecContext(ctx, query, template)
	if err != nil {
		return fmt.Errorf("không thể cập nhật template: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("lỗi khi kiểm tra số dòng bị ảnh hưởng: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("không tìm thấy template để cập nhật")
	}

	return nil
}

// Delete xóa template
func (r *TemplateRepository) Delete(ctx context.Context, themeID, templateID int) error {
	query := `
		DELETE FROM templates
		WHERE template_id = ? AND theme_id = ?
	`

	result, err := r.db.ExecContext(ctx, query, templateID, themeID)
	if err != nil {
		return fmt.Errorf("không thể xóa template: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("lỗi khi kiểm tra số dòng bị ảnh hưởng: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("không tìm thấy template để xóa")
	}

	return nil
}

// List lấy danh sách templates với phân trang
func (r *TemplateRepository) List(ctx context.Context, themeID int, req request.ListTemplateRequest) ([]*models.Template, string, bool, error) {
	// Set default sort nếu không được chỉ định
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortDir == "" {
		req.SortDir = "DESC"
	}

	// Setup query cơ bản
	baseQuery := `
		SELECT * FROM templates
		WHERE theme_id = ?
	`

	// Thêm điều kiện tìm kiếm nếu có
	params := []interface{}{themeID}
	if req.Search != "" {
		baseQuery += " AND (name LIKE ? OR type LIKE ?)"
		searchPattern := "%" + req.Search + "%"
		params = append(params, searchPattern, searchPattern)
	}

	// Thêm điều kiện phân trang
	if req.Cursor != "" {
		// Xác định điều kiện so sánh dựa trên hướng sắp xếp
		operator := ">"
		if req.SortDir == "DESC" {
			operator = "<"
		}

		// Thêm điều kiện để lấy các bản ghi sau cursor
		baseQuery += fmt.Sprintf(" AND %s %s ?", req.SortBy, operator)
		params = append(params, req.Cursor)
	}

	// Thêm sắp xếp và giới hạn
	baseQuery += fmt.Sprintf(" ORDER BY %s %s LIMIT ?", req.SortBy, req.SortDir)
	params = append(params, req.Limit+1) // +1 để kiểm tra hasMore

	// Thực thi query
	var templates []*models.Template
	err := r.db.SelectContext(ctx, &templates, baseQuery, params...)
	if err != nil {
		return nil, "", false, fmt.Errorf("không thể lấy danh sách templates: %w", err)
	}

	// Xác định nếu còn dữ liệu
	hasMore := false
	if len(templates) > req.Limit {
		hasMore = true
		templates = templates[:req.Limit] // Cắt bỏ phần tử cuối
	}

	// Xác định nextCursor
	var nextCursor string
	if len(templates) > 0 && hasMore {
		lastTemplate := templates[len(templates)-1]
		switch req.SortBy {
		case "created_at":
			nextCursor = lastTemplate.CreatedAt.Format(time.RFC3339)
		case "updated_at":
			nextCursor = lastTemplate.UpdatedAt.Format(time.RFC3339)
		case "name":
			nextCursor = lastTemplate.Name
		default:
			nextCursor = fmt.Sprintf("%v", lastTemplate.TemplateID)
		}
	}

	return templates, nextCursor, hasMore, nil
}

// ListByType lấy danh sách templates theo loại với phân trang
func (r *TemplateRepository) ListByType(ctx context.Context, themeID int, templateType string, req request.ListTemplateRequest) ([]*models.Template, string, bool, error) {
	// Set default sort nếu không được chỉ định
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortDir == "" {
		req.SortDir = "DESC"
	}

	// Setup query cơ bản
	baseQuery := `
		SELECT * FROM templates
		WHERE theme_id = ? AND type = ?
	`

	// Thêm điều kiện tìm kiếm nếu có
	params := []interface{}{themeID, templateType}
	if req.Search != "" {
		baseQuery += " AND name LIKE ?"
		searchPattern := "%" + req.Search + "%"
		params = append(params, searchPattern)
	}

	// Thêm điều kiện phân trang
	if req.Cursor != "" {
		// Xác định điều kiện so sánh dựa trên hướng sắp xếp
		operator := ">"
		if req.SortDir == "DESC" {
			operator = "<"
		}

		// Thêm điều kiện để lấy các bản ghi sau cursor
		baseQuery += fmt.Sprintf(" AND %s %s ?", req.SortBy, operator)
		params = append(params, req.Cursor)
	}

	// Thêm sắp xếp và giới hạn
	baseQuery += fmt.Sprintf(" ORDER BY %s %s LIMIT ?", req.SortBy, req.SortDir)
	params = append(params, req.Limit+1) // +1 để kiểm tra hasMore

	// Thực thi query
	var templates []*models.Template
	err := r.db.SelectContext(ctx, &templates, baseQuery, params...)
	if err != nil {
		return nil, "", false, fmt.Errorf("không thể lấy danh sách templates theo loại: %w", err)
	}

	// Xác định nếu còn dữ liệu
	hasMore := false
	if len(templates) > req.Limit {
		hasMore = true
		templates = templates[:req.Limit] // Cắt bỏ phần tử cuối
	}

	// Xác định nextCursor
	var nextCursor string
	if len(templates) > 0 && hasMore {
		lastTemplate := templates[len(templates)-1]
		switch req.SortBy {
		case "created_at":
			nextCursor = lastTemplate.CreatedAt.Format(time.RFC3339)
		case "updated_at":
			nextCursor = lastTemplate.UpdatedAt.Format(time.RFC3339)
		case "name":
			nextCursor = lastTemplate.Name
		default:
			nextCursor = fmt.Sprintf("%v", lastTemplate.TemplateID)
		}
	}

	return templates, nextCursor, hasMore, nil
}

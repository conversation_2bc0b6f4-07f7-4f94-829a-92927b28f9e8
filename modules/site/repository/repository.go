package repository

import (
	"context"

	"github.com/webnew/wn-backend-v2/modules/site/dto/request"
	"github.com/webnew/wn-backend-v2/modules/site/models"
)

// WebsiteRepository định ngh<PERSON>a interface cho repository của Website
type WebsiteRepository interface {
	// <PERSON><PERSON><PERSON> thao tác CRUD cơ bản
	Create(ctx context.Context, website *models.Website) error
	GetByID(ctx context.Context, tenantID, websiteID int) (*models.Website, error)
	GetBySubdomain(ctx context.Context, subdomain string) (*models.Website, error)
	GetByCustomDomain(ctx context.Context, customDomain string) (*models.Website, error)
	Update(ctx context.Context, website *models.Website) error
	Delete(ctx context.Context, tenantID, websiteID int) error
	List(ctx context.Context, tenantID int, req request.ListWebsiteRequest) ([]*models.Website, string, bool, error)
}

// PageRepository định nghĩa interface cho repository của Page
type PageRepository interface {
	// <PERSON><PERSON><PERSON> thao tác CRUD cơ bản
	Create(ctx context.Context, page *models.Page) error
	GetByID(ctx context.Context, websiteID, pageID int) (*models.Page, error)
	GetBySlug(ctx context.Context, websiteID int, slug string) (*models.Page, error)
	GetHomepage(ctx context.Context, websiteID int) (*models.Page, error)
	Update(ctx context.Context, page *models.Page) error
	Delete(ctx context.Context, websiteID, pageID int) error
	List(ctx context.Context, websiteID int, req request.ListPageRequest) ([]*models.Page, string, bool, error)
}

// ThemeRepository định nghĩa interface cho repository của Theme
type ThemeRepository interface {
	// Các thao tác CRUD cơ bản
	Create(ctx context.Context, theme *models.Theme) error
	GetByID(ctx context.Context, themeID int) (*models.Theme, error)
	Update(ctx context.Context, theme *models.Theme) error
	Delete(ctx context.Context, themeID int) error
	List(ctx context.Context, req request.ListThemeRequest) ([]*models.Theme, string, bool, error)

	// Các thao tác khác
	ListPublic(ctx context.Context, req request.ListThemeRequest) ([]*models.Theme, string, bool, error)
}

// TemplateRepository định nghĩa interface cho repository của Template
type TemplateRepository interface {
	// Các thao tác CRUD cơ bản
	Create(ctx context.Context, template *models.Template) error
	GetByID(ctx context.Context, themeID, templateID int) (*models.Template, error)
	Update(ctx context.Context, template *models.Template) error
	Delete(ctx context.Context, themeID, templateID int) error
	List(ctx context.Context, themeID int, req request.ListTemplateRequest) ([]*models.Template, string, bool, error)

	// Các thao tác khác
	ListByType(ctx context.Context, themeID int, templateType string, req request.ListTemplateRequest) ([]*models.Template, string, bool, error)
}

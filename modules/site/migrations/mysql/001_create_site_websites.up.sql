CREATE TABLE IF NOT EXISTS site_websites (
  website_id INT UNSIGNED NOT NULL AUTO_INCREMENT,
  tenant_id INT UNSIGNED NOT NULL,
  name VARCHAR(100) NOT NULL,
  subdomain VARCHAR(50) NULL,
  custom_domain VARCHAR(255) NULL,
  description TEXT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'draft',
  theme_id INT UNSIGNED NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (website_id),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_subdomain (subdomain),
  INDEX idx_custom_domain (custom_domain),
  INDEX idx_theme_id (theme_id),
  UNIQUE INDEX unique_tenant_subdomain (tenant_id, subdomain),
  UNIQUE INDEX unique_custom_domain (custom_domain)
)
ENGINE = InnoDB
CHARACTER SET = utf8mb4
COLLATE = utf8mb4_unicode_ci; 
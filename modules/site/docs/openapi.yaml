openapi: 3.1.0
info:
  title: Website Management API
  description: API quản lý website, trang, theme và template
  version: 1.0.0
servers:
  - url: http://wn-api.local
    description: Local development server
paths:
  /api/v1/site/websites:
    get:
      summary: <PERSON><PERSON><PERSON>nh sách website
      operationId: listWebsites
      parameters:
        - name: cursor
          in: query
          description: Cursor để phân trang
          required: false
          schema:
            type: string
        - name: limit
          in: query
          description: Số lượng kết quả trả về
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebsiteListResponse'
    post:
      summary: Tạo website mới
      operationId: createWebsite
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateWebsiteRequest'
      responses:
        '201':
          description: Website đã được tạo
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebsiteResponse'
  /api/v1/site/websites/{id}:
    get:
      summary: <PERSON><PERSON><PERSON> thông tin website theo ID
      operationId: getWebsite
      parameters:
        - name: id
          in: path
          description: ID của website
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebsiteResponse'
        '404':
          description: Website không tồn tại
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      summary: Cập nhật website
      operationId: updateWebsite
      parameters:
        - name: id
          in: path
          description: ID của website
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateWebsiteRequest'
      responses:
        '200':
          description: Website đã được cập nhật
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebsiteResponse'
        '404':
          description: Website không tồn tại
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      summary: Xóa website
      operationId: deleteWebsite
      parameters:
        - name: id
          in: path
          description: ID của website
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Website đã được xóa
        '404':
          description: Website không tồn tại
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/site/website-pages/{website_id}:
    get:
      summary: Lấy danh sách trang của website
      operationId: listPages
      parameters:
        - name: website_id
          in: path
          description: ID của website
          required: true
          schema:
            type: integer
        - name: cursor
          in: query
          description: Cursor để phân trang
          required: false
          schema:
            type: string
        - name: limit
          in: query
          description: Số lượng kết quả trả về
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageListResponse'
    post:
      summary: Tạo trang mới cho website
      operationId: createPage
      parameters:
        - name: website_id
          in: path
          description: ID của website
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePageRequest'
      responses:
        '201':
          description: Trang đã được tạo
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageResponse'

  /api/v1/site/website-pages/{website_id}/page/{id}:
    get:
      summary: Lấy thông tin trang theo ID
      operationId: getPage
      parameters:
        - name: website_id
          in: path
          description: ID của website
          required: true
          schema:
            type: integer
        - name: id
          in: path
          description: ID của trang
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageResponse'
        '404':
          description: Trang không tồn tại
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      summary: Cập nhật trang
      operationId: updatePage
      parameters:
        - name: website_id
          in: path
          description: ID của website
          required: true
          schema:
            type: integer
        - name: id
          in: path
          description: ID của trang
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePageRequest'
      responses:
        '200':
          description: Trang đã được cập nhật
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageResponse'
        '404':
          description: Trang không tồn tại
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      summary: Xóa trang
      operationId: deletePage
      parameters:
        - name: website_id
          in: path
          description: ID của website
          required: true
          schema:
            type: integer
        - name: id
          in: path
          description: ID của trang
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Trang đã được xóa
        '404':
          description: Trang không tồn tại
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/site/website-pages/{website_id}/homepage:
    get:
      summary: Lấy trang chủ của website
      operationId: getHomepage
      parameters:
        - name: website_id
          in: path
          description: ID của website
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageResponse'
        '404':
          description: Không tìm thấy trang chủ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/site/website-pages/{website_id}/by-slug/{slug}:
    get:
      summary: Lấy trang theo slug
      operationId: getPageBySlug
      parameters:
        - name: website_id
          in: path
          description: ID của website
          required: true
          schema:
            type: integer
        - name: slug
          in: path
          description: Slug của trang
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageResponse'
        '404':
          description: Không tìm thấy trang
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
                
  /api/v1/site/themes:
    get:
      summary: Lấy danh sách theme
      operationId: listThemes
      parameters:
        - name: cursor
          in: query
          description: Cursor để phân trang
          required: false
          schema:
            type: string
        - name: limit
          in: query
          description: Số lượng kết quả trả về
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ThemeListResponse'
    post:
      summary: Tạo theme mới
      operationId: createTheme
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateThemeRequest'
      responses:
        '201':
          description: Theme đã được tạo
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ThemeResponse'

  /api/v1/site/themes/{id}:
    get:
      summary: Lấy thông tin theme theo ID
      operationId: getTheme
      parameters:
        - name: id
          in: path
          description: ID của theme
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ThemeResponse'
        '404':
          description: Theme không tồn tại
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      summary: Cập nhật theme
      operationId: updateTheme
      parameters:
        - name: id
          in: path
          description: ID của theme
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateThemeRequest'
      responses:
        '200':
          description: Theme đã được cập nhật
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ThemeResponse'
        '404':
          description: Theme không tồn tại
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      summary: Xóa theme
      operationId: deleteTheme
      parameters:
        - name: id
          in: path
          description: ID của theme
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Theme đã được xóa
        '404':
          description: Theme không tồn tại
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/site/themes/public:
    get:
      summary: Lấy danh sách theme công khai
      operationId: listPublicThemes
      parameters:
        - name: cursor
          in: query
          description: Cursor để phân trang
          required: false
          schema:
            type: string
        - name: limit
          in: query
          description: Số lượng kết quả trả về
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ThemeListResponse'

  /api/v1/site/theme-templates/{theme_id}:
    get:
      summary: Lấy danh sách template của theme
      operationId: listTemplates
      parameters:
        - name: theme_id
          in: path
          description: ID của theme
          required: true
          schema:
            type: integer
        - name: cursor
          in: query
          description: Cursor để phân trang
          required: false
          schema:
            type: string
        - name: limit
          in: query
          description: Số lượng kết quả trả về
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateListResponse'
    post:
      summary: Tạo template mới
      operationId: createTemplate
      parameters:
        - name: theme_id
          in: path
          description: ID của theme
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTemplateRequest'
      responses:
        '201':
          description: Template đã được tạo
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateResponse'

  /api/v1/site/theme-templates/{theme_id}/template/{id}:
    get:
      summary: Lấy thông tin template theo ID
      operationId: getTemplate
      parameters:
        - name: theme_id
          in: path
          description: ID của theme
          required: true
          schema:
            type: integer
        - name: id
          in: path
          description: ID của template
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateResponse'
        '404':
          description: Template không tồn tại
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      summary: Cập nhật template
      operationId: updateTemplate
      parameters:
        - name: theme_id
          in: path
          description: ID của theme
          required: true
          schema:
            type: integer
        - name: id
          in: path
          description: ID của template
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTemplateRequest'
      responses:
        '200':
          description: Template đã được cập nhật
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateResponse'
        '404':
          description: Template không tồn tại
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      summary: Xóa template
      operationId: deleteTemplate
      parameters:
        - name: theme_id
          in: path
          description: ID của theme
          required: true
          schema:
            type: integer
        - name: id
          in: path
          description: ID của template
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Template đã được xóa
        '404':
          description: Template không tồn tại
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/site/theme-templates/{theme_id}/by-type/{type}:
    get:
      summary: Lấy danh sách template theo loại
      operationId: listTemplatesByType
      parameters:
        - name: theme_id
          in: path
          description: ID của theme
          required: true
          schema:
            type: integer
        - name: type
          in: path
          description: Loại template (page, section, blog, landing, ecommerce, portfolio)
          required: true
          schema:
            type: string
        - name: cursor
          in: query
          description: Cursor để phân trang
          required: false
          schema:
            type: string
        - name: limit
          in: query
          description: Số lượng kết quả trả về
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateListResponse'

components:
  schemas:
    Status:
      type: object
      properties:
        code:
          type: integer
          example: 200
        message:
          type: string
          example: "Operation completed successfully"
        success:
          type: boolean
          example: true
        error_code:
          type: string
          nullable: true
          example: null
        path:
          type: string
          example: "/api/v1/site/websites"
        timestamp:
          type: string
          format: date-time
          example: "2025-03-15T14:35:22Z"
        details:
          type: array
          nullable: true
          items:
            type: object
            properties:
              field:
                type: string
              message:
                type: string
          example: null

    Meta:
      type: object
      properties:
        next_cursor:
          type: string
          nullable: true
        has_more:
          type: boolean

    Website:
      type: object
      properties:
        id:
          type: integer
        tenant_id:
          type: integer
        name:
          type: string
        subdomain:
          type: string
          nullable: true
        custom_domain:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        status:
          type: string
          enum: [draft, active, inactive, maintenance, building, deleted]
        theme_id:
          type: integer
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        theme:
          $ref: '#/components/schemas/Theme'
          nullable: true

    CreateWebsiteRequest:
      type: object
      required:
        - name
        - status
      properties:
        name:
          type: string
          maxLength: 100
        subdomain:
          type: string
          nullable: true
          maxLength: 50
        custom_domain:
          type: string
          nullable: true
          maxLength: 255
        description:
          type: string
          nullable: true
        status:
          type: string
          enum: [draft, active, inactive, maintenance, building, deleted]
        theme_id:
          type: integer
          nullable: true

    UpdateWebsiteRequest:
      type: object
      properties:
        name:
          type: string
          maxLength: 100
        subdomain:
          type: string
          nullable: true
          maxLength: 50
        custom_domain:
          type: string
          nullable: true
          maxLength: 255
        description:
          type: string
          nullable: true
        status:
          type: string
          enum: [draft, active, inactive, maintenance, building, deleted]
        theme_id:
          type: integer
          nullable: true

    Page:
      type: object
      properties:
        id:
          type: integer
        website_id:
          type: integer
        title:
          type: string
        slug:
          type: string
        content:
          type: object
          additionalProperties: true
        layout:
          type: string
          nullable: true
        meta_title:
          type: string
          nullable: true
        meta_description:
          type: string
          nullable: true
        is_homepage:
          type: boolean
        status:
          type: string
          enum: [draft, published, archived]
        published_at:
          type: string
          format: date-time
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        website:
          $ref: '#/components/schemas/Website'
          nullable: true

    CreatePageRequest:
      type: object
      required:
        - title
        - slug
      properties:
        title:
          type: string
          maxLength: 255
        slug:
          type: string
          maxLength: 255
        content:
          type: object
          additionalProperties: true
        layout:
          type: string
          nullable: true
          maxLength: 100
        meta_title:
          type: string
          nullable: true
          maxLength: 255
        meta_description:
          type: string
          nullable: true
        is_homepage:
          type: boolean
          nullable: true
        status:
          type: string
          enum: [draft, published, scheduled]
        published_at:
          type: string
          format: date-time
          nullable: true

    UpdatePageRequest:
      type: object
      properties:
        title:
          type: string
          maxLength: 255
        slug:
          type: string
          maxLength: 255
        content:
          type: object
          additionalProperties: true
        layout:
          type: string
          nullable: true
          maxLength: 100
        meta_title:
          type: string
          nullable: true
          maxLength: 255
        meta_description:
          type: string
          nullable: true
        is_homepage:
          type: boolean
          nullable: true
        status:
          type: string
          enum: [draft, published, scheduled]
        published_at:
          type: string
          format: date-time
          nullable: true

    Theme:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        description:
          type: string
          nullable: true
        thumbnail_url:
          type: string
          nullable: true
        is_public:
          type: boolean
        created_by:
          type: integer
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        templates:
          type: array
          items:
            $ref: '#/components/schemas/Template'
          nullable: true

    CreateThemeRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
        description:
          type: string
          nullable: true
        thumbnail_url:
          type: string
          nullable: true
        is_public:
          type: boolean

    UpdateThemeRequest:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
          nullable: true
        thumbnail_url:
          type: string
          nullable: true
        is_public:
          type: boolean

    Template:
      type: object
      properties:
        id:
          type: integer
        theme_id:
          type: integer
        name:
          type: string
        type:
          type: string
          enum: [page, section, blog, landing, ecommerce, portfolio]
        html_structure:
          type: string
        css:
          type: string
          nullable: true
        default_content:
          type: object
          additionalProperties: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        theme:
          $ref: '#/components/schemas/Theme'
          nullable: true

    CreateTemplateRequest:
      type: object
      required:
        - name
        - type
        - html_structure
      properties:
        name:
          type: string
        type:
          type: string
          enum: [page, section, blog, landing, ecommerce, portfolio]
        html_structure:
          type: string
        css:
          type: string
          nullable: true
        default_content:
          type: object
          additionalProperties: true

    UpdateTemplateRequest:
      type: object
      properties:
        name:
          type: string
        type:
          type: string
          enum: [page, section, blog, landing, ecommerce, portfolio]
        html_structure:
          type: string
        css:
          type: string
          nullable: true
        default_content:
          type: object
          additionalProperties: true

    WebsiteResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          $ref: '#/components/schemas/Website'

    WebsiteListResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          type: array
          items:
            $ref: '#/components/schemas/Website'
        meta:
          $ref: '#/components/schemas/Meta'

    PageResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          $ref: '#/components/schemas/Page'

    PageListResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          type: array
          items:
            $ref: '#/components/schemas/Page'
        meta:
          $ref: '#/components/schemas/Meta'

    ThemeResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          $ref: '#/components/schemas/Theme'

    ThemeListResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          type: array
          items:
            $ref: '#/components/schemas/Theme'
        meta:
          $ref: '#/components/schemas/Meta'

    TemplateResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          $ref: '#/components/schemas/Template'

    TemplateListResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          type: array
          items:
            $ref: '#/components/schemas/Template'
        meta:
          $ref: '#/components/schemas/Meta'
          
    ErrorResponse:
      type: object
      properties:
        status:
          type: object
          properties:
            code:
              type: integer
              example: 404
            message:
              type: string
              example: "Resource not found"
            success:
              type: boolean
              example: false
            error_code:
              type: string
              example: "RESOURCE_NOT_FOUND"
            path:
              type: string
              example: "/api/v1/site/websites/999"
            timestamp:
              type: string
              format: date-time
              example: "2025-03-15T14:30:45Z"
            details:
              type: array
              items:
                type: object
                properties:
                  field:
                    type: string
                  message:
                    type: string

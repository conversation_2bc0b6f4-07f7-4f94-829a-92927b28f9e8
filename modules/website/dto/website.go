package dto

import "time"

// Pagination chứa thông tin phân trang
type Pagination struct {
	CurrentPage int   `json:"current_page"`
	PageSize    int   `json:"page_size"`
	TotalItems  int64 `json:"total_items"`
	TotalPages  int   `json:"total_pages"`
	HasNext     bool  `json:"has_next"`
	HasPrev     bool  `json:"has_prev"`
}

// Page DTOs
// ------------------------------

// CreatePageRequest chứa thông tin để tạo trang mới
type CreatePageRequest struct {
	Title         string `json:"title" binding:"required,max=200"`
	Content       string `json:"content" binding:"required"`
	Description   string `json:"description" binding:"max=500"`
	Published     bool   `json:"published" binding:"omitempty"`
	FeaturedImage string `json:"featured_image" binding:"omitempty"`
}

// UpdatePageRequest chứa thông tin để cập nhật trang
type UpdatePageRequest struct {
	Title         *string `json:"title" binding:"omitempty,max=200"`
	Content       *string `json:"content" binding:"omitempty"`
	Description   *string `json:"description" binding:"omitempty,max=500"`
	Published     *bool   `json:"published" binding:"omitempty"`
	FeaturedImage *string `json:"featured_image" binding:"omitempty"`
}

// PageResponse chứa thông tin trả về về trang
type PageResponse struct {
	ID            int64      `json:"id"`
	Title         string     `json:"title"`
	Slug          string     `json:"slug"`
	Content       string     `json:"content"`
	Description   string     `json:"description"`
	Published     bool       `json:"published"`
	PublishedAt   *time.Time `json:"published_at,omitempty"`
	FeaturedImage string     `json:"featured_image"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
}

// ListPagesParams chứa các tham số để lọc và phân trang danh sách trang
type ListPagesParams struct {
	Page         int    `form:"page" binding:"omitempty,min=1"`
	PageSize     int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	SortBy       string `form:"sort_by" binding:"omitempty"`
	SortOrder    string `form:"sort_order" binding:"omitempty,oneof=asc desc"`
	PublishedOnly bool  `form:"published_only" binding:"omitempty"`
	Search       string `form:"search" binding:"omitempty"`
}

// ListPagesResponse chứa kết quả danh sách trang có phân trang
type ListPagesResponse struct {
	Pages      []PageResponse `json:"pages"`
	Pagination Pagination     `json:"pagination"`
}

// Menu DTOs
// ------------------------------

// CreateMenuRequest chứa thông tin để tạo menu mới
type CreateMenuRequest struct {
	Name     string `json:"name" binding:"required,max=100"`
	Position string `json:"position" binding:"required,max=50"`
	IsActive bool   `json:"is_active" binding:"omitempty"`
}

// UpdateMenuRequest chứa thông tin để cập nhật menu
type UpdateMenuRequest struct {
	Name     *string `json:"name" binding:"omitempty,max=100"`
	Position *string `json:"position" binding:"omitempty,max=50"`
	IsActive *bool   `json:"is_active" binding:"omitempty"`
}

// MenuItemDTO chứa thông tin về mục menu
type MenuItemDTO struct {
	ID        int64         `json:"id"`
	ParentID  *int64        `json:"parent_id"`
	Title     string        `json:"title"`
	URL       string        `json:"url"`
	Target    string        `json:"target"`
	Order     int           `json:"order"`
	IsActive  bool          `json:"is_active"`
	Children  []MenuItemDTO `json:"children,omitempty"`
	CreatedAt time.Time     `json:"created_at"`
	UpdatedAt time.Time     `json:"updated_at"`
}

// MenuResponse chứa thông tin trả về về menu
type MenuResponse struct {
	ID        int64         `json:"id"`
	Name      string        `json:"name"`
	Position  string        `json:"position"`
	IsActive  bool          `json:"is_active"`
	Items     []MenuItemDTO `json:"items"`
	CreatedAt time.Time     `json:"created_at"`
	UpdatedAt time.Time     `json:"updated_at"`
}

// ListMenusParams chứa các tham số để lọc và phân trang danh sách menu
type ListMenusParams struct {
	Page      int    `form:"page" binding:"omitempty,min=1"`
	PageSize  int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	SortBy    string `form:"sort_by" binding:"omitempty"`
	SortOrder string `form:"sort_order" binding:"omitempty,oneof=asc desc"`
	ActiveOnly bool  `form:"active_only" binding:"omitempty"`
	Position  string `form:"position" binding:"omitempty"`
}

// ListMenusResponse chứa kết quả danh sách menu có phân trang
type ListMenusResponse struct {
	Menus      []MenuResponse `json:"menus"`
	Pagination Pagination     `json:"pagination"`
}

// Menu Item DTOs
// ------------------------------

// CreateMenuItemRequest chứa thông tin để tạo mục menu mới
type CreateMenuItemRequest struct {
	MenuID   int64  `json:"menu_id" binding:"required"`
	ParentID *int64 `json:"parent_id" binding:"omitempty"`
	Title    string `json:"title" binding:"required,max=100"`
	URL      string `json:"url" binding:"required"`
	Target   string `json:"target" binding:"omitempty,max=20"`
	Order    int    `json:"order" binding:"omitempty"`
	IsActive bool   `json:"is_active" binding:"omitempty"`
}

// UpdateMenuItemRequest chứa thông tin để cập nhật mục menu
type UpdateMenuItemRequest struct {
	ParentID *int64  `json:"parent_id" binding:"omitempty"`
	Title    *string `json:"title" binding:"omitempty,max=100"`
	URL      *string `json:"url" binding:"omitempty"`
	Target   *string `json:"target" binding:"omitempty,max=20"`
	Order    *int    `json:"order" binding:"omitempty"`
	IsActive *bool   `json:"is_active" binding:"omitempty"`
}

// MenuItemResponse chứa thông tin trả về về mục menu
type MenuItemResponse struct {
	ID        int64     `json:"id"`
	MenuID    int64     `json:"menu_id"`
	ParentID  *int64    `json:"parent_id"`
	Title     string    `json:"title"`
	URL       string    `json:"url"`
	Target    string    `json:"target"`
	Order     int       `json:"order"`
	IsActive  bool      `json:"is_active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Banner DTOs
// ------------------------------

// CreateBannerRequest chứa thông tin để tạo banner mới
type CreateBannerRequest struct {
	Title     string     `json:"title" binding:"required,max=200"`
	ImageURL  string     `json:"image_url" binding:"required"`
	LinkURL   string     `json:"link_url" binding:"omitempty"`
	Position  string     `json:"position" binding:"required,max=50"`
	Order     int        `json:"order" binding:"omitempty"`
	IsActive  bool       `json:"is_active" binding:"omitempty"`
	StartDate *time.Time `json:"start_date" binding:"omitempty"`
	EndDate   *time.Time `json:"end_date" binding:"omitempty"`
}

// UpdateBannerRequest chứa thông tin để cập nhật banner
type UpdateBannerRequest struct {
	Title     *string    `json:"title" binding:"omitempty,max=200"`
	ImageURL  *string    `json:"image_url" binding:"omitempty"`
	LinkURL   *string    `json:"link_url" binding:"omitempty"`
	Position  *string    `json:"position" binding:"omitempty,max=50"`
	Order     *int       `json:"order" binding:"omitempty"`
	IsActive  *bool      `json:"is_active" binding:"omitempty"`
	StartDate *time.Time `json:"start_date" binding:"omitempty"`
	EndDate   *time.Time `json:"end_date" binding:"omitempty"`
}

// BannerResponse chứa thông tin trả về về banner
type BannerResponse struct {
	ID        int64      `json:"id"`
	Title     string     `json:"title"`
	ImageURL  string     `json:"image_url"`
	LinkURL   string     `json:"link_url"`
	Position  string     `json:"position"`
	Order     int        `json:"order"`
	IsActive  bool       `json:"is_active"`
	StartDate *time.Time `json:"start_date,omitempty"`
	EndDate   *time.Time `json:"end_date,omitempty"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
}

// ListBannersParams chứa các tham số để lọc và phân trang danh sách banner
type ListBannersParams struct {
	Page       int    `form:"page" binding:"omitempty,min=1"`
	PageSize   int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	SortBy     string `form:"sort_by" binding:"omitempty"`
	SortOrder  string `form:"sort_order" binding:"omitempty,oneof=asc desc"`
	ActiveOnly bool   `form:"active_only" binding:"omitempty"`
	Position   string `form:"position" binding:"omitempty"`
}

// ListBannersResponse chứa kết quả danh sách banner có phân trang
type ListBannersResponse struct {
	Banners    []BannerResponse `json:"banners"`
	Pagination Pagination       `json:"pagination"`
}

// ActiveBannersResponse chứa kết quả danh sách banner đang hoạt động
type ActiveBannersResponse struct {
	Banners []BannerResponse `json:"banners"`
}

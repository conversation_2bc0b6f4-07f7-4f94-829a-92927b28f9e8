FROM golang:1.23-alpine AS development

# Cài đặt công cụ hot-reload Air
RUN go install github.com/air-verse/air@latest
RUN go install github.com/go-delve/delve/cmd/dlv@latest
RUN apk add --no-cache git gcc musl-dev

# Thiết lập thư mục làm việc
WORKDIR /app/modules/cart

# Copy file go.mod và go.sum
COPY modules/cart/go.mod .
COPY modules/cart/go.sum* .
COPY pkg/go.mod /app/pkg/
COPY pkg/go.sum /app/pkg/

# Tải các dependency
RUN go mod download

# Copy mã nguồn của module
COPY modules/cart/ .
COPY pkg/ /app/pkg/

# Để Air hoạt động chính xác, chúng ta cần có quyền ghi vào thư mục tmp
RUN mkdir -p tmp && chmod 777 tmp

# Command mặc định cho môi trường phát triển
CMD ["air", "-c", ".air.toml"]

# Stage build cho production
FROM golang:1.23-alpine AS builder

WORKDIR /app/modules/cart

# Copy file go.mod và go.sum
COPY modules/cart/go.mod .
COPY modules/cart/go.sum* .
COPY pkg/go.mod /app/pkg/
COPY pkg/go.sum /app/pkg/

# Tải các dependency
RUN go mod download

# Copy mã nguồn của module
COPY modules/cart/ .
COPY pkg/ /app/pkg/

# Build ứng dụng
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o cart ./cmd/main.go

# Stage final cho production
FROM alpine:latest AS production

RUN apk --no-cache add ca-certificates tzdata

WORKDIR /app

# Copy binary từ stage builder
COPY --from=builder /app/modules/cart/cart .

# Command mặc định cho môi trường production
CMD ["./cart"]

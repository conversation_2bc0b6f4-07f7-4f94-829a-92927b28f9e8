# Cart module configuration
server:
  host: "0.0.0.0"
  port: 9055

db:
  host: "localhost"
  port: 3307
  username: "root"
  password: "root"
  database: "blog_v4"

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0

tracing:
  enabled: true
  service_name: "cart-service"
  exporter_type: "jaeger"
  signoz:
    endpoint: "localhost:4317"
  jaeger:
    host: "localhost"
    port: "6831"
  sample_ratio: 1.0

api:
  base_path: "/api/v1/cart"

jwt:
  access_signing_key: "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
  refresh_signing_key: "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
  access_token_expiration: "168h"
  refresh_token_expiration: "168h"
  issuer: "wn-backend"

package handlers

import (
	"net/http"
	"strconv"
	"time"

	"wnapi/internal/core"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/cart/dto/request"
	"wnapi/modules/cart/internal"
	"wnapi/modules/cart/service"
)

// CartHandler handles HTTP requests for carts
type CartHandler struct {
	cartService service.CartService
	jwtService  *auth.JWTService
	logger      logger.Logger
}

// NewCartHandler creates a new cart handler instance
func NewCartHandler(cartService service.CartService, jwtService *auth.JWTService) *CartHandler {
	return &CartHandler{
		cartService: cartService,
		jwtService:  jwtService,
		logger:      logger.GetLogger("CartHandler"),
	}
}

// GetOrCreateCart handles the request to get or create a cart
func (h *CartHandler) GetOrCreateCart(c *core.Context) {
	ctx := c.Request.Context()
	h.logger.Debug("Getting or creating cart")

	var req request.GetOrCreateCartRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	// Get user ID from token if available
	userID, _ := auth.GetUserIDFromContext(c)
	if userID > 0 && req.UserID == 0 {
		req.UserID = int64(userID)
	}

	// If no user ID and no session ID, generate a random session ID
	if req.UserID == 0 && req.SessionID == "" {
		// Simple session ID generation
		req.SessionID = "sess_" + strconv.FormatInt(time.Now().UnixNano(), 10)
	}

	result, err := h.cartService.GetOrCreateCart(ctx, req)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to get or create cart")
		return
	}

	apiSuccess(c, http.StatusOK, "Cart retrieved successfully", result)
}

// GetCartByID handles the request to get a cart by ID
func (h *CartHandler) GetCartByID(c *core.Context) {
	ctx := c.Request.Context()
	h.logger.Debug("Getting cart by ID")

	cartID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidCartID, err.Error())
		return
	}

	result, err := h.cartService.GetCartWithItems(ctx, cartID)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to get cart")
		return
	}

	apiSuccess(c, http.StatusOK, "Cart retrieved successfully", result)
}

// UpdateCart handles the request to update a cart
func (h *CartHandler) UpdateCart(c *core.Context) {
	ctx := c.Request.Context()
	h.logger.Debug("Updating cart")

	cartID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidCartID, err.Error())
		return
	}

	var req request.UpdateCartRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	result, err := h.cartService.UpdateCart(ctx, cartID, req)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to update cart")
		return
	}

	apiSuccess(c, http.StatusOK, "Cart updated successfully", result)
}

// DeleteCart handles the request to delete a cart
func (h *CartHandler) DeleteCart(c *core.Context) {
	ctx := c.Request.Context()
	h.logger.Debug("Deleting cart")

	cartID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidCartID, err.Error())
		return
	}

	if err := h.cartService.DeleteCart(ctx, cartID); err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to delete cart")
		return
	}

	apiSuccess(c, http.StatusOK, "Cart deleted successfully", nil)
}

// ListCarts handles the request to list carts
func (h *CartHandler) ListCarts(c *core.Context) {
	ctx := c.Request.Context()
	h.logger.Debug("Listing carts")

	var req request.ListCartRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	// Get tenant ID from token
	tenantID, err := auth.GetTenantIDFromContext(c)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, "Missing tenant ID", err.Error())
		return
	}

	// Convert tenantID from uint to int64
	result, err := h.cartService.ListCarts(ctx, int64(tenantID), req)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to list carts")
		return
	}

	apiSuccess(c, http.StatusOK, "Carts retrieved successfully", result)
}

// AddItemToCart handles the request to add an item to a cart
func (h *CartHandler) AddItemToCart(c *core.Context) {
	ctx := c.Request.Context()
	h.logger.Debug("Adding item to cart")

	cartID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidCartID, err.Error())
		return
	}

	var req request.AddCartItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	result, err := h.cartService.AddItemToCart(ctx, cartID, req)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to add item to cart")
		return
	}

	apiSuccess(c, http.StatusCreated, "Item added to cart successfully", result)
}

// UpdateCartItem handles the request to update a cart item
func (h *CartHandler) UpdateCartItem(c *core.Context) {
	ctx := c.Request.Context()
	h.logger.Debug("Updating cart item")

	itemID, err := strconv.ParseInt(c.Param("itemId"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidItemID, err.Error())
		return
	}

	var req request.UpdateCartItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	result, err := h.cartService.UpdateCartItem(ctx, itemID, req)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to update cart item")
		return
	}

	apiSuccess(c, http.StatusOK, "Cart item updated successfully", result)
}

// RemoveCartItem handles the request to remove an item from a cart
func (h *CartHandler) RemoveCartItem(c *core.Context) {
	ctx := c.Request.Context()
	h.logger.Debug("Removing cart item")

	itemID, err := strconv.ParseInt(c.Param("itemId"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidItemID, err.Error())
		return
	}

	if err := h.cartService.RemoveCartItem(ctx, itemID); err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to remove cart item")
		return
	}

	apiSuccess(c, http.StatusOK, "Cart item removed successfully", nil)
}

// AddAddonToCartItem handles the request to add an addon to a cart item
func (h *CartHandler) AddAddonToCartItem(c *core.Context) {
	ctx := c.Request.Context()
	h.logger.Debug("Adding addon to cart item")

	itemID, err := strconv.ParseInt(c.Param("itemId"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidItemID, err.Error())
		return
	}

	var req request.AddCartItemAddonRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	result, err := h.cartService.AddAddonToCartItem(ctx, itemID, req)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to add addon to cart item")
		return
	}

	apiSuccess(c, http.StatusCreated, "Addon added to cart item successfully", result)
}

// RemoveAddonFromCartItem handles the request to remove an addon from a cart item
func (h *CartHandler) RemoveAddonFromCartItem(c *core.Context) {
	ctx := c.Request.Context()
	h.logger.Debug("Removing addon from cart item")

	addonID, err := strconv.ParseInt(c.Param("addonId"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, "Invalid addon ID", err.Error())
		return
	}

	if err := h.cartService.RemoveAddonFromCartItem(ctx, addonID); err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to remove addon from cart item")
		return
	}

	apiSuccess(c, http.StatusOK, "Addon removed from cart item successfully", nil)
}

// RecalculateCart handles the request to recalculate a cart
func (h *CartHandler) RecalculateCart(c *core.Context) {
	ctx := c.Request.Context()
	h.logger.Debug("Recalculating cart")

	cartID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidCartID, err.Error())
		return
	}

	result, err := h.cartService.RecalculateCart(ctx, cartID)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to recalculate cart")
		return
	}

	apiSuccess(c, http.StatusOK, "Cart recalculated successfully", result)
}

// ApplyCoupon handles the request to apply a coupon to a cart
func (h *CartHandler) ApplyCoupon(c *core.Context) {
	ctx := c.Request.Context()
	h.logger.Debug("Applying coupon to cart")

	cartID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidCartID, err.Error())
		return
	}

	var req request.ApplyCouponRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	result, err := h.cartService.ApplyCoupon(ctx, cartID, req.CouponCode)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to apply coupon")
		return
	}

	apiSuccess(c, http.StatusOK, "Coupon applied successfully", result)
}

// ConvertCartToOrder handles the request to convert a cart to an order
func (h *CartHandler) ConvertCartToOrder(c *core.Context) {
	ctx := c.Request.Context()
	h.logger.Debug("Converting cart to order")

	cartID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidCartID, err.Error())
		return
	}

	var req request.ConvertCartToOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	orderReferenceID, err := h.cartService.ConvertCartToOrder(ctx, cartID, req.OrderType)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to convert cart to order")
		return
	}

	apiSuccess(c, http.StatusOK, "Cart converted to order successfully", gin.H{
		"order_reference_id": orderReferenceID,
	})
}

// MergeAnonymousCart handles the request to merge an anonymous cart into a user's cart
func (h *CartHandler) MergeAnonymousCart(c *core.Context) {
	ctx := c.Request.Context()
	h.logger.Debug("Merging anonymous cart")

	var req request.MergeAnonymousCartRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	result, err := h.cartService.MergeAnonymousCart(ctx, req.SessionID, req.UserID)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to merge carts")
		return
	}

	apiSuccess(c, http.StatusOK, "Carts merged successfully", result)
}

package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/webnew/wn-backend-v2/modules/cart/configs"
	"github.com/webnew/wn-backend-v2/modules/cart/dto/request"
	"github.com/webnew/wn-backend-v2/modules/cart/service"
	"github.com/webnew/wn-backend-v2/modules/cart/tracing"
	"github.com/webnew/wn-backend-v2/pkg/auth"
)

// CartHandler handles HTTP requests for carts
type CartHandler struct {
	cartService service.CartService
	jwtService  *auth.JWTService
	config      *configs.Config
}

// NewCartHandler creates a new cart handler instance
func NewCartHandler(cartService service.CartService, jwtService *auth.JWTService, cfg *configs.Config) *CartHandler {
	return &CartHandler{
		cartService: cartService,
		jwtService:  jwtService,
		config:      cfg,
	}
}

// GetOrCreateCart handles the request to get or create a cart
func (h *<PERSON>tHandler) GetOrCreateCart(c *gin.Context) {
	ctx, span := tracing.StartSpan(c.Request.Context(), "CartHandler.GetOrCreateCart")
	defer span.End()

	var req request.GetOrCreateCartRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	// Get user ID from token if available
	userID, _ := auth.GetUserIDFromContext(c)
	if userID > 0 && req.UserID == 0 {
		req.UserID = int64(userID)
	}

	// If no user ID and no session ID, generate a random session ID
	if req.UserID == 0 && req.SessionID == "" {
		// Simple session ID generation
		req.SessionID = "sess_" + strconv.FormatInt(time.Now().UnixNano(), 10)
	}

	result, err := h.cartService.GetOrCreateCart(ctx, req)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to get or create cart")
		return
	}

	apiSuccess(c, http.StatusOK, "Cart retrieved successfully", result)
}

// GetCartByID handles the request to get a cart by ID
func (h *CartHandler) GetCartByID(c *gin.Context) {
	ctx, span := tracing.StartSpan(c.Request.Context(), "CartHandler.GetCartByID")
	defer span.End()

	cartID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidCartID, err.Error())
		return
	}

	result, err := h.cartService.GetCartWithItems(ctx, cartID)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to get cart")
		return
	}

	apiSuccess(c, http.StatusOK, "Cart retrieved successfully", result)
}

// UpdateCart handles the request to update a cart
func (h *CartHandler) UpdateCart(c *gin.Context) {
	ctx, span := tracing.StartSpan(c.Request.Context(), "CartHandler.UpdateCart")
	defer span.End()

	cartID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidCartID, err.Error())
		return
	}

	var req request.UpdateCartRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	result, err := h.cartService.UpdateCart(ctx, cartID, req)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to update cart")
		return
	}

	apiSuccess(c, http.StatusOK, "Cart updated successfully", result)
}

// DeleteCart handles the request to delete a cart
func (h *CartHandler) DeleteCart(c *gin.Context) {
	ctx, span := tracing.StartSpan(c.Request.Context(), "CartHandler.DeleteCart")
	defer span.End()

	cartID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidCartID, err.Error())
		return
	}

	if err := h.cartService.DeleteCart(ctx, cartID); err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to delete cart")
		return
	}

	apiSuccess(c, http.StatusOK, "Cart deleted successfully", nil)
}

// ListCarts handles the request to list carts
func (h *CartHandler) ListCarts(c *gin.Context) {
	ctx, span := tracing.StartSpan(c.Request.Context(), "CartHandler.ListCarts")
	defer span.End()

	var req request.ListCartRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	// Get tenant ID from token
	tenantID, err := auth.GetTenantIDFromContext(c)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, "Missing tenant ID", err.Error())
		return
	}

	// Convert tenantID from uint to int64
	result, err := h.cartService.ListCarts(ctx, int64(tenantID), req)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to list carts")
		return
	}

	apiSuccess(c, http.StatusOK, "Carts retrieved successfully", result)
}

// AddItemToCart handles the request to add an item to a cart
func (h *CartHandler) AddItemToCart(c *gin.Context) {
	ctx, span := tracing.StartSpan(c.Request.Context(), "CartHandler.AddItemToCart")
	defer span.End()

	cartID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidCartID, err.Error())
		return
	}

	var req request.AddCartItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	result, err := h.cartService.AddItemToCart(ctx, cartID, req)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to add item to cart")
		return
	}

	apiSuccess(c, http.StatusCreated, "Item added to cart successfully", result)
}

// UpdateCartItem handles the request to update a cart item
func (h *CartHandler) UpdateCartItem(c *gin.Context) {
	ctx, span := tracing.StartSpan(c.Request.Context(), "CartHandler.UpdateCartItem")
	defer span.End()

	itemID, err := strconv.ParseInt(c.Param("itemId"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidItemID, err.Error())
		return
	}

	var req request.UpdateCartItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	result, err := h.cartService.UpdateCartItem(ctx, itemID, req)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to update cart item")
		return
	}

	apiSuccess(c, http.StatusOK, "Cart item updated successfully", result)
}

// RemoveCartItem handles the request to remove an item from a cart
func (h *CartHandler) RemoveCartItem(c *gin.Context) {
	ctx, span := tracing.StartSpan(c.Request.Context(), "CartHandler.RemoveCartItem")
	defer span.End()

	itemID, err := strconv.ParseInt(c.Param("itemId"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidItemID, err.Error())
		return
	}

	if err := h.cartService.RemoveCartItem(ctx, itemID); err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to remove cart item")
		return
	}

	apiSuccess(c, http.StatusOK, "Cart item removed successfully", nil)
}

// AddAddonToCartItem handles the request to add an addon to a cart item
func (h *CartHandler) AddAddonToCartItem(c *gin.Context) {
	ctx, span := tracing.StartSpan(c.Request.Context(), "CartHandler.AddAddonToCartItem")
	defer span.End()

	itemID, err := strconv.ParseInt(c.Param("itemId"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidItemID, err.Error())
		return
	}

	var req request.AddCartItemAddonRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	result, err := h.cartService.AddAddonToCartItem(ctx, itemID, req)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to add addon to cart item")
		return
	}

	apiSuccess(c, http.StatusCreated, "Addon added to cart item successfully", result)
}

// RemoveAddonFromCartItem handles the request to remove an addon from a cart item
func (h *CartHandler) RemoveAddonFromCartItem(c *gin.Context) {
	ctx, span := tracing.StartSpan(c.Request.Context(), "CartHandler.RemoveAddonFromCartItem")
	defer span.End()

	addonID, err := strconv.ParseInt(c.Param("addonId"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, "Invalid addon ID", err.Error())
		return
	}

	if err := h.cartService.RemoveAddonFromCartItem(ctx, addonID); err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to remove addon from cart item")
		return
	}

	apiSuccess(c, http.StatusOK, "Addon removed from cart item successfully", nil)
}

// RecalculateCart handles the request to recalculate a cart
func (h *CartHandler) RecalculateCart(c *gin.Context) {
	ctx, span := tracing.StartSpan(c.Request.Context(), "CartHandler.RecalculateCart")
	defer span.End()

	cartID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidCartID, err.Error())
		return
	}

	result, err := h.cartService.RecalculateCart(ctx, cartID)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to recalculate cart")
		return
	}

	apiSuccess(c, http.StatusOK, "Cart recalculated successfully", result)
}

// ApplyCoupon handles the request to apply a coupon to a cart
func (h *CartHandler) ApplyCoupon(c *gin.Context) {
	ctx, span := tracing.StartSpan(c.Request.Context(), "CartHandler.ApplyCoupon")
	defer span.End()

	cartID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidCartID, err.Error())
		return
	}

	var req request.ApplyCouponRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	result, err := h.cartService.ApplyCoupon(ctx, cartID, req.CouponCode)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to apply coupon")
		return
	}

	apiSuccess(c, http.StatusOK, "Coupon applied successfully", result)
}

// ConvertCartToOrder handles the request to convert a cart to an order
func (h *CartHandler) ConvertCartToOrder(c *gin.Context) {
	ctx, span := tracing.StartSpan(c.Request.Context(), "CartHandler.ConvertCartToOrder")
	defer span.End()

	cartID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, MsgInvalidCartID, err.Error())
		return
	}

	var req request.ConvertCartToOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	orderReferenceID, err := h.cartService.ConvertCartToOrder(ctx, cartID, req.OrderType)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to convert cart to order")
		return
	}

	apiSuccess(c, http.StatusOK, "Cart converted to order successfully", gin.H{
		"order_reference_id": orderReferenceID,
	})
}

// MergeAnonymousCart handles the request to merge an anonymous cart into a user's cart
func (h *CartHandler) MergeAnonymousCart(c *gin.Context) {
	ctx, span := tracing.StartSpan(c.Request.Context(), "CartHandler.MergeAnonymousCart")
	defer span.End()

	var req request.MergeAnonymousCartRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleValidationError(c, err)
		return
	}

	result, err := h.cartService.MergeAnonymousCart(ctx, req.SessionID, req.UserID)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Failed to merge carts")
		return
	}

	apiSuccess(c, http.StatusOK, "Carts merged successfully", result)
}

package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/webnew/wn-backend-v2/pkg/response"
)

// Constants for error codes
const (
	ErrCodeInvalidRequestFormat = "INVALID_REQUEST_FORMAT"
	ErrCodeUnauthorized         = "UNAUTHORIZED"
	ErrCodeInternalServerError  = "INTERNAL_SERVER_ERROR"
	ErrCodeCartNotFound         = "CART_NOT_FOUND"
	ErrCodeCartItemNotFound     = "CART_ITEM_NOT_FOUND"
	ErrCodeAddonNotFound        = "ADDON_NOT_FOUND"
	ErrCodeInvalidItemType      = "INVALID_ITEM_TYPE"
	ErrCodeInvalidAddonType     = "INVALID_ADDON_TYPE"
)

// Constants for error messages
const (
	MsgInvalidCartID  = "Invalid cart ID"
	MsgInvalidItemID  = "Invalid item ID"
	MsgInvalidAddonID = "Invalid addon ID"
)

// apiSuccess sends a successful API response
func apiSuccess(c *gin.Context, statusCode int, message string, data interface{}) {
	response.Success(c, statusCode, message, data)
}

// apiSuccessWithMeta sends a successful API response with metadata
func apiSuccessWithMeta(c *gin.Context, statusCode int, message string, data interface{}, meta map[string]interface{}) {
	response.SuccessWithMeta(c, statusCode, message, data, meta)
}

// apiError sends an error API response
func apiError(c *gin.Context, statusCode int, errorCode string, message string) {
	response.ErrorWithDetails(c, statusCode, message, errorCode, nil)
}

// apiErrorWithDetails sends an error API response with detailed information
func apiErrorWithDetails(c *gin.Context, statusCode int, errorCode string, message string, details interface{}) {
	response.ErrorWithDetails(c, statusCode, message, errorCode, details)
}

// handleValidationError handles validation errors
func handleValidationError(c *gin.Context, err error) {
	details := []interface{}{map[string]string{"message": err.Error()}}
	response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", ErrCodeInvalidRequestFormat, details)
}

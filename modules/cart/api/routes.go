package api

import (
	"github.com/gin-gonic/gin"
	"github.com/webnew/wn-backend-v2/modules/cart/api/handlers"
	"github.com/webnew/wn-backend-v2/modules/cart/configs"
	"github.com/webnew/wn-backend-v2/modules/cart/service"
	"github.com/webnew/wn-backend-v2/pkg/auth"
	pkgMiddleware "github.com/webnew/wn-backend-v2/pkg/middleware"
)

// RegisterRoutes registers all API routes for the cart module
func RegisterRoutes(
	router *gin.Engine,
	cartService service.CartService,
	jwtService *auth.JWTService,
	permService pkgMiddleware.PermissionService,
	config *configs.Config,
) {
	// Create handlers
	cartHandler := handlers.NewCartHandler(cartService, jwtService, config)

	// API v1 group
	v1 := router.Group("/api/v1")
	{
		// Cart routes
		cartsGroup := v1.Group("/carts")
		{
			// Public routes (no authentication required)
			cartsGroup.POST("", cartHandler.GetOrCreateCart)

			// Routes requiring authentication
			authedCartsGroup := cartsGroup.Group("", pkgMiddleware.RequireAuth(jwtService))
			{
				// List carts (requires permission)
				authedCartsGroup.GET("", pkgMiddleware.RequirePermission(permService, "cart.carts.list"), cartHandler.ListCarts)

				// Get, update, delete single cart
				authedCartsGroup.GET("/:id", cartHandler.GetCartByID)
				authedCartsGroup.PUT("/:id", cartHandler.UpdateCart)
				authedCartsGroup.DELETE("/:id", pkgMiddleware.RequirePermission(permService, "cart.carts.delete"), cartHandler.DeleteCart)

				// Cart item operations
				authedCartsGroup.POST("/:id/items", cartHandler.AddItemToCart)
				authedCartsGroup.PUT("/items/:itemId", cartHandler.UpdateCartItem)
				authedCartsGroup.DELETE("/items/:itemId", cartHandler.RemoveCartItem)

				// Cart item addon operations
				authedCartsGroup.POST("/items/:itemId/addons", cartHandler.AddAddonToCartItem)
				authedCartsGroup.DELETE("/addons/:addonId", cartHandler.RemoveAddonFromCartItem)

				// Cart operations
				authedCartsGroup.POST("/:id/recalculate", cartHandler.RecalculateCart)
				authedCartsGroup.POST("/:id/apply-coupon", cartHandler.ApplyCoupon)
				authedCartsGroup.POST("/:id/convert-to-order", pkgMiddleware.RequirePermission(permService, "cart.carts.convert"), cartHandler.ConvertCartToOrder)
				authedCartsGroup.POST("/merge", pkgMiddleware.RequirePermission(permService, "cart.carts.merge"), cartHandler.MergeAnonymousCart)
			}
		}
	}
}

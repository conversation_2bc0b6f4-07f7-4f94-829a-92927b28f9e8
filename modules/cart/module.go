package cart

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"

	"github.com/webnew/wn-backend-v2/modules/cart/api"
	"github.com/webnew/wn-backend-v2/modules/cart/configs"
	"github.com/webnew/wn-backend-v2/modules/cart/repository/mysql"
	"github.com/webnew/wn-backend-v2/modules/cart/service"
	"github.com/webnew/wn-backend-v2/modules/cart/tracing"
	"github.com/webnew/wn-backend-v2/pkg/auth"
	pkgMiddleware "github.com/webnew/wn-backend-v2/pkg/middleware"
)

// Module represents the cart module
type Module struct {
	DB          *sqlx.DB
	Config      *configs.Config
	server      *http.Server
	router      *gin.Engine
	cartService service.CartService
	permService pkgMiddleware.PermissionService
}

// NewModule creates a new instance of the cart module
func NewModule(db *sqlx.DB) *Module {
	return &Module{
		DB: db,
	}
}

// NewModuleWithConfig creates a new instance of the cart module with configuration
func NewModuleWithConfig(cfg *configs.Config) *Module {
	// Set up database connection
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true",
		cfg.DB.Username,
		cfg.DB.Password,
		cfg.DB.Host,
		cfg.DB.Port,
		cfg.DB.Database,
	)

	db, err := sqlx.Connect("mysql", dsn)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Set connection pool settings
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	// Khởi tạo tracing
	if err := tracing.InitTracing(cfg); err != nil {
		log.Printf("WARN: Không thể khởi tạo tracing: %v", err)
	}

	return &Module{
		DB:     db,
		Config: cfg,
		router: gin.Default(),
	}
}

// RegisterRoutes registers all routes for the cart module
func (m *Module) RegisterRoutes(router *gin.Engine) {
	// Create repositories
	cartRepo := mysql.NewCartRepository(m.DB)
	cartItemRepo := mysql.NewCartItemRepository(m.DB)
	cartItemAddonRepo := mysql.NewCartItemAddonRepository(m.DB)
	cartSummaryRepo := mysql.NewCartSummaryRepository(m.DB)

	// Create services
	cartService := service.NewCartService(cartRepo, cartItemRepo, cartItemAddonRepo, cartSummaryRepo)

	// Save services in module for potential external use
	m.cartService = cartService

	// Create JWT service
	accessTokenExpiration, err := time.ParseDuration(m.Config.JWT.AccessTokenExpiration)
	if err != nil {
		log.Printf("Error parsing access token expiration: %v, using default 24h", err)
		accessTokenExpiration = 24 * time.Hour
	}

	refreshTokenExpiration, err := time.ParseDuration(m.Config.JWT.RefreshTokenExpiration)
	if err != nil {
		log.Printf("Error parsing refresh token expiration: %v, using default 720h", err)
		refreshTokenExpiration = 720 * time.Hour
	}

	jwtConfig := auth.JWTConfig{
		AccessSigningKey:       m.Config.JWT.AccessSigningKey,
		RefreshSigningKey:      m.Config.JWT.RefreshSigningKey,
		AccessTokenExpiration:  accessTokenExpiration,
		RefreshTokenExpiration: refreshTokenExpiration,
		Issuer:                 m.Config.JWT.Issuer,
	}
	jwtService := auth.NewJWTService(jwtConfig)

	// Create default permission service if none is provided
	if m.permService == nil {
		m.permService = NewDefaultPermissionService()
	}

	// Register routes
	api.RegisterRoutes(router, cartService, jwtService, m.permService, m.Config)
}

// Start starts the cart module server
func (m *Module) Start() error {
	// If router is not set, use the one from the module
	if m.router == nil {
		m.router = gin.Default()
	}

	m.router.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	// Register routes
	m.RegisterRoutes(m.router)

	// Create HTTP server
	m.server = &http.Server{
		Addr:    fmt.Sprintf("%s:%d", m.Config.Server.Host, m.Config.Server.Port),
		Handler: m.router,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting cart service on %s:%d", m.Config.Server.Host, m.Config.Server.Port)
		if err := m.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	return nil
}

// Stop gracefully stops the cart module server
func (m *Module) Stop() error {
	if m.server == nil {
		return nil
	}

	// Create a context with timeout for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Shutdown server
	if err := m.server.Shutdown(ctx); err != nil {
		return fmt.Errorf("server shutdown failed: %w", err)
	}

	// Close database connection
	if m.DB != nil {
		if err := m.DB.Close(); err != nil {
			return fmt.Errorf("database connection close failed: %w", err)
		}
	}

	// Đóng tracer
	tracing.CloseTracer()

	log.Println("Cart service stopped gracefully")
	return nil
}

// DefaultPermissionService là một triển khai đơn giản của PermissionService
type DefaultPermissionService struct{}

// NewDefaultPermissionService tạo một default permission service mới
func NewDefaultPermissionService() *DefaultPermissionService {
	return &DefaultPermissionService{}
}

// CheckPermission kiểm tra quyền của người dùng (luôn trả về true)
func (s *DefaultPermissionService) CheckPermission(ctx *gin.Context, tenantID, userID uint, permission string) (bool, error) {
	// Mặc định cho phép tất cả các quyền
	return true, nil
}

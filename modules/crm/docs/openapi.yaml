openapi: 3.1.0
info:
  title: CRM API
  description: API cho module quản lý khách hàng (CRM)
  version: 1.0.0
servers:
  - url: http://wn-api.local
    description: API Server mặc định
paths:
  /api/v1/customers:
    get:
      summary: Danh sách khách hàng
      description: L<PERSON><PERSON> danh sách khách hàng với phân trang
      tags:
        - customers
      security:
        - bearerAuth: []
      parameters:
        - name: cursor
          in: query
          description: Con trỏ phân trang
          required: false
          schema:
            type: string
        - name: limit
          in: query
          description: Số lượng khách hàng trả về
          required: false
          schema:
            type: integer
            default: 10
        - name: search
          in: query
          description: Tìm kiếm khách hàng theo tên, email hoặc số điện thoại
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Danh sách khách hàng
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomersSuccessResponse'
        '401':
          description: <PERSON>hông được xác thực
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      summary: T<PERSON><PERSON> khách hàng mới
      description: Tạo một khách hàng mới với thông tin được cung cấp
      tags:
        - customers
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomerRequest'
      responses:
        '201':
          description: Khách hàng được tạo thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerSuccessResponse'
        '400':
          description: Yêu cầu không hợp lệ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Email đã tồn tại cho tenant này
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /api/v1/customers/{id}:
    get:
      summary: Lấy thông tin khách hàng
      description: Lấy thông tin khách hàng theo ID
      tags:
        - customers
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          description: ID khách hàng
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Thông tin khách hàng
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerSuccessResponse'
        '404':
          description: Không tìm thấy khách hàng
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      summary: Cập nhật khách hàng
      description: Cập nhật thông tin khách hàng
      tags:
        - customers
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          description: ID khách hàng
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCustomerRequest'
      responses:
        '200':
          description: Khách hàng được cập nhật thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerSuccessResponse'
        '400':
          description: Yêu cầu không hợp lệ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Không tìm thấy khách hàng
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Email đã tồn tại cho tenant này
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      summary: Xóa khách hàng
      description: Xóa khách hàng theo ID
      tags:
        - customers
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          description: ID khách hàng
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Khách hàng đã được xóa thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmptySuccessResponse'
        '404':
          description: Không tìm thấy khách hàng
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    CreateCustomerRequest:
      type: object
      required:
        - email
        - full_name
      properties:
        email:
          type: string
          format: email
          example: <EMAIL>
        full_name:
          type: string
          example: John Doe
        phone:
          type: string
          example: "+84987654321"
        address:
          type: string
          example: "123 Main St, City"
        notes:
          type: string
          example: "VIP customer"
        status:
          type: string
          enum: [active, inactive, blocked]
          example: active
        is_verified:
          type: boolean
          example: false
    UpdateCustomerRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          example: <EMAIL>
        full_name:
          type: string
          example: John Doe
        phone:
          type: string
          example: "+84987654321"
        address:
          type: string
          example: "123 Main St, City"
        notes:
          type: string
          example: "VIP customer"
        status:
          type: string
          enum: [active, inactive, blocked]
          example: active
        is_verified:
          type: boolean
          example: true
    CustomerResponse:
      type: object
      properties:
        customer_id:
          type: integer
          example: 1
        tenant_id:
          type: integer
          example: 1
        email:
          type: string
          example: <EMAIL>
        full_name:
          type: string
          example: John Doe
        phone:
          type: string
          example: "+84987654321"
        address:
          type: string
          example: "123 Main St, City"
        notes:
          type: string
          example: "VIP customer"
        status:
          type: string
          example: active
        is_verified:
          type: boolean
          example: true
        created_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
        created_by:
          type: integer
          example: 1
        updated_by:
          type: integer
          example: 1
    Status:
      type: object
      properties:
        code:
          type: integer
          example: 200
        message:
          type: string
          example: "Operation completed successfully"
        success:
          type: boolean
          example: true
        error_code:
          type: string
          nullable: true
          example: null
        path:
          type: string
          example: "/api/v1/users"
        timestamp:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
        details:
          type: array
          nullable: true
          items:
            type: object
            properties:
              field:
                type: string
                example: "email"
              message:
                type: string
                example: "Email is already in use"
    Meta:
      type: object
      properties:
        next_cursor:
          type: string
          example: "dXNlcl9pZDoxMA=="
        has_more:
          type: boolean
          example: true
    CustomerSuccessResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          $ref: '#/components/schemas/CustomerResponse'
    CustomersSuccessResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          type: array
          items:
            $ref: '#/components/schemas/CustomerResponse'
        meta:
          $ref: '#/components/schemas/Meta'
    EmptySuccessResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          type: object
          nullable: true
    ErrorResponse:
      type: object
      properties:
        status:
          type: object
          properties:
            code:
              type: integer
              example: 400
            message:
              type: string
              example: "Invalid request data"
            success:
              type: boolean
              example: false
            error_code:
              type: string
              example: "INVALID_REQUEST"
            path:
              type: string
              example: "/api/v1/users"
            timestamp:
              type: string
              format: date-time
              example: "2023-01-01T12:00:00Z"
            details:
              type: array
              items:
                type: object
                properties:
                  field:
                    type: string
                    example: "email"
                  message:
                    type: string
                    example: "Invalid email format"

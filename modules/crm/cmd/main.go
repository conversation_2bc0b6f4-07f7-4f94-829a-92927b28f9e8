package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	_ "github.com/go-sql-driver/mysql"
	crmmodule "github.com/webnew/wn-backend-v2/modules/crm"
)

func main() {
	// Khởi tạo module với backoff retry
	var module *crmmodule.Module
	var startErr error

	// Thử khởi động module với backoff
	maxRetries := 5
	for i := 0; i < maxRetries; i++ {
		// Khởi tạo module
		module = crmmodule.NewModuleWithConfig()

		// Khởi động server
		startErr = module.Start()
		if startErr == nil {
			break // Nếu thành công thì thoát khỏi vòng lặp
		}

		// Có lỗi xảy ra, thử lại sau một khoảng thời gian
		retryDelay := time.Duration(i+1) * time.Second
		log.Printf("Failed to start server (attempt %d/%d): %v. Retrying in %v...",
			i+1, maxRetries, startErr, retryDelay)

		// Đợi trước khi thử lại
		time.Sleep(retryDelay)
	}

	// Nếu vẫn không khởi động được sau các lần thử
	if startErr != nil {
		log.Fatalf("Failed to start server after %d attempts: %v", maxRetries, startErr)
	}

	// Channel để nhận signal khi shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// Đợi signal để shutdown
	<-quit
	log.Println("Shutting down servers...")

	// Stop server
	if err := module.Stop(); err != nil {
		log.Printf("Failed to stop server: %v", err)
	}

	log.Println("Servers exited properly")
}

server:
  host: "0.0.0.0"
  port: 9043

db:
  host: "localhost"
  port: 3307
  username: "root"
  password: "root"
  database: "blog_v4"

redis:
  host: "localhost"
  port: 6379
  password: ""

tracing:
  enabled: true
  service_name: "blog-service"
  exporter_type: "jaeger"
  signoz:
    endpoint: "localhost:4317"
  jaeger:
    host: "localhost"
    port: "6831"
  sample_ratio: 1.0

api:
  base_path: "/api/v1/blog"

jwt:
  access_signing_key: "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
  refresh_signing_key: "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
  access_token_expiration: "168h"
  refresh_token_expiration: "168h"
  issuer: wn-backend
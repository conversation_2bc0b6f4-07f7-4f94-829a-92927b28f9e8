package configs

import (
	"encoding/json"
	"fmt"
	"os"
	"strconv"

	"github.com/spf13/viper"
)

// Config represents the configuration for the blog module
type Config struct {
	// Server configuration
	Server struct {
		Host string `mapstructure:"host"`
		Port int    `mapstructure:"port"`
	} `mapstructure:"server"`

	// Database configuration
	DB struct {
		Host     string `mapstructure:"host"`
		Port     int    `mapstructure:"port"`
		Username string `mapstructure:"username"`
		Password string `mapstructure:"password"`
		Database string `mapstructure:"database"`
	} `mapstructure:"db"`

	// Redis configuration
	Redis struct {
		Host     string `mapstructure:"host"`
		Port     int    `mapstructure:"port"`
		Password string `mapstructure:"password"`
		DB       int    `mapstructure:"db"`
	} `mapstructure:"redis"`

	// Tracing configuration
	Tracing struct {
		Enabled      bool    `mapstructure:"enabled"`
		ServiceName  string  `mapstructure:"service_name"`
		ExporterType string  `mapstructure:"exporter_type"`
		SampleRatio  float64 `mapstructure:"sample_ratio"`
		Signoz       struct {
			Endpoint string `mapstructure:"endpoint"`
		} `mapstructure:"signoz"`
		J<PERSON>ger struct {
			Host string `mapstructure:"host"`
			Port string `mapstructure:"port"`
		} `mapstructure:"jaeger"`
	} `mapstructure:"tracing"`

	// API configuration
	API struct {
		BasePath string `mapstructure:"base_path"`
	} `mapstructure:"api"`

	// JWT Configuration
	JWT struct {
		AccessSigningKey       string `mapstructure:"access_signing_key"`
		RefreshSigningKey      string `mapstructure:"refresh_signing_key"`
		AccessTokenExpiration  string `mapstructure:"access_token_expiration"`
		RefreshTokenExpiration string `mapstructure:"refresh_token_expiration"`
		Issuer                 string `mapstructure:"issuer"`
	} `mapstructure:"jwt"`
}

// LoadConfig loads the configuration from config files
func LoadConfig() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")

	// Look for config in the current directory
	viper.AddConfigPath(".")
	// Also look in the configs directory
	viper.AddConfigPath("./configs")
	// And one level up in case we're running from a subdirectory
	viper.AddConfigPath("../configs")

	// Read the config file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			return nil, fmt.Errorf("config file not found: %w", err)
		}
		return nil, fmt.Errorf("error reading config file: %w", err)
	}

	// Print the config file used
	fmt.Printf("Using config file: %s\n", viper.ConfigFileUsed())

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("unable to decode config into struct: %w", err)
	}

	// Override with environment variables if set
	if dbHost := os.Getenv("DB_HOST"); dbHost != "" {
		config.DB.Host = dbHost
	}

	if dbPort := os.Getenv("DB_PORT"); dbPort != "" {
		if port, err := strconv.Atoi(dbPort); err == nil {
			config.DB.Port = port
		}
	}

	if dbUser := os.Getenv("DB_USER"); dbUser != "" {
		config.DB.Username = dbUser
	}

	if dbPass := os.Getenv("DB_PASSWORD"); dbPass != "" {
		config.DB.Password = dbPass
	}

	if dbName := os.Getenv("DB_NAME"); dbName != "" {
		config.DB.Database = dbName
	}

	if redisHost := os.Getenv("REDIS_HOST"); redisHost != "" {
		config.Redis.Host = redisHost
	}

	if redisPort := os.Getenv("REDIS_PORT"); redisPort != "" {
		if port, err := strconv.Atoi(redisPort); err == nil {
			config.Redis.Port = port
		}
	}

	if httpPort := os.Getenv("HTTP_PORT"); httpPort != "" {
		if port, err := strconv.Atoi(httpPort); err == nil {
			config.Server.Port = port
		}
	}

	// Handle tracing configuration from environment variables
	if exporterType := os.Getenv("TRACING_EXPORTER_TYPE"); exporterType != "" {
		config.Tracing.ExporterType = exporterType
		config.Tracing.Enabled = true
	}

	if serviceName := os.Getenv("TRACING_SERVICE_NAME"); serviceName != "" {
		config.Tracing.ServiceName = serviceName
	}

	// Handle tracing endpoint from environment
	if tracingEndpoint := os.Getenv("TRACING_ENDPOINT"); tracingEndpoint != "" {
		config.Tracing.Signoz.Endpoint = tracingEndpoint
		config.Tracing.Enabled = true
	}

	// Handle Jaeger specific environment variables
	jaegerHost := os.Getenv("JAEGER_AGENT_HOST")
	jaegerPort := os.Getenv("JAEGER_AGENT_PORT")
	if jaegerHost != "" {
		config.Tracing.Jaeger.Host = jaegerHost
		config.Tracing.Enabled = true
		config.Tracing.ExporterType = "jaeger"
	}
	if jaegerPort != "" {
		config.Tracing.Jaeger.Port = jaegerPort
	}

	// Print config in Docker environment
	if os.Getenv("DOCKER_ENV") == "true" {
		PrintConfig(&config)
	}

	// Set default values if not provided
	if config.Tracing.ServiceName == "" {
		config.Tracing.ServiceName = "blog-service"
	}

	if config.Tracing.ExporterType == "" {
		config.Tracing.ExporterType = "signoz"
	}

	if config.Tracing.SampleRatio == 0 {
		config.Tracing.SampleRatio = 1.0
	}

	return &config, nil
}

// PrintConfig prints all configuration values in a formatted JSON
func PrintConfig(config *Config) {
	// Create a copy of the config with the password masked for security
	configCopy := *config
	configCopy.DB.Password = "********"
	configCopy.Redis.Password = "********"

	// Marshal config to JSON for pretty printing
	configJSON, err := json.MarshalIndent(configCopy, "", "  ")
	if err != nil {
		fmt.Printf("Error marshaling config: %v\n", err)
		return
	}

	fmt.Printf("=== BLOG MODULE CONFIGURATION ===\n%s\n==============================\n", string(configJSON))
}

// CreateDefaultConfig creates a default config file if none exists
func CreateDefaultConfig() error {
	// Check if config file already exists
	if _, err := os.Stat("configs/config.yaml"); err == nil {
		return fmt.Errorf("config file already exists")
	}

	// Create default configuration
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")

	// Set default values
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 8082)

	viper.SetDefault("db.host", "mysql")
	viper.SetDefault("db.port", 3306)
	viper.SetDefault("db.username", "root")
	viper.SetDefault("db.password", "password")
	viper.SetDefault("db.database", "blog_v4")

	viper.SetDefault("redis.host", "redis")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 0)

	viper.SetDefault("tracing.enabled", false)
	viper.SetDefault("tracing.service_name", "blog-service")
	viper.SetDefault("tracing.exporter_type", "signoz")
	viper.SetDefault("tracing.signoz.endpoint", "localhost:4317")
	viper.SetDefault("tracing.jaeger.host", "localhost")
	viper.SetDefault("tracing.jaeger.port", "6831")
	viper.SetDefault("tracing.sample_ratio", 1.0)

	viper.SetDefault("api.base_path", "/api/v1/blog")

	// Create configs directory if it doesn't exist
	if err := os.MkdirAll("configs", 0755); err != nil {
		return fmt.Errorf("failed to create configs directory: %w", err)
	}

	// Write config file
	if err := viper.SafeWriteConfig(); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	fmt.Println("Default config file created at configs/config.yaml")
	return nil
}

package service

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/blog/dto"
	"wnapi/modules/blog/internal"

	"github.com/gosimple/slug"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
)

// Service implements BlogService interface
type Service struct {
	repo   internal.Repository
	config internal.BlogConfig
	logger logger.Logger
}

// NewService creates a new blog service
func NewService(repo internal.Repository, config internal.BlogConfig, log logger.Logger) internal.BlogService {
	return &Service{
		repo:   repo,
		config: config,
		logger: log,
	}
}

// CreatePost creates a new blog post
func (s *Service) CreatePost(ctx context.Context, req dto.CreatePostRequest) (*dto.PostResponse, error) {
	ctx, span := tracing.StartSpan(ctx, "blog-service", "create_post")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("blog.title", req.Title),
		attribute.Int("blog.category_id", req.CategoryID),
		attribute.String("blog.status", req.Status),
	)

	// Get user ID from context (assumes middleware has set this)
	userID, ok := ctx.Value("user_id").(int)
	if !ok {
		tracing.SetSpanStatus(ctx, codes.Error, "User not authenticated")
		return nil, internal.ErrNotAuthorized
	}

	// Generate slug if not provided
	postSlug := req.Slug
	if postSlug == "" {
		postSlug = slug.Make(req.Title)
	} else {
		// Sanitize slug
		postSlug = slug.Make(postSlug)
	}

	// Validate slug uniqueness
	existingPost, err := s.repo.GetPostBySlug(ctx, postSlug)
	if err != nil && !errors.Is(err, internal.ErrPostNotFound) {
		tracing.RecordError(ctx, err)
		return nil, err
	}
	
	if existingPost != nil {
		tracing.SetSpanStatus(ctx, codes.Error, "Duplicate slug")
		return nil, internal.ErrDuplicateSlug
	}

	// Set publish time if needed
	var publishedAt *time.Time
	if req.PublishNow && req.Status == "published" {
		now := time.Now()
		publishedAt = &now
	}

	// Create post entity
	post := &internal.Post{
		Title:         req.Title,
		Slug:          postSlug,
		Content:       req.Content,
		AuthorID:      userID,
		CategoryID:    req.CategoryID,
		Status:        req.Status,
		FeaturedImage: req.FeaturedImage,
		PublishedAt:   publishedAt,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// Save to database
	if err := s.repo.CreatePost(ctx, post); err != nil {
		tracing.RecordError(ctx, err)
		s.logger.Error("Failed to create post", logger.String("error", err.Error()))
		return nil, err
	}

	// Get category name if available
	categoryName := ""
	if post.CategoryID > 0 {
		category, err := s.repo.GetCategoryByID(ctx, post.CategoryID)
		if err == nil && category != nil {
			categoryName = category.Name
		}
	}

	// Prepare response
	response := &dto.PostResponse{
		ID:            post.ID,
		Title:         post.Title,
		Slug:          post.Slug,
		Content:       post.Content,
		AuthorID:      post.AuthorID,
		CategoryID:    post.CategoryID,
		CategoryName:  categoryName,
		Status:        post.Status,
		FeaturedImage: post.FeaturedImage,
		PublishedAt:   post.PublishedAt,
		CreatedAt:     post.CreatedAt,
		UpdatedAt:     post.UpdatedAt,
	}

	tracing.SetSpanStatus(ctx, codes.Ok, "Post created successfully")
	tracing.AddSpanAttributes(ctx, attribute.Int("blog.post_id", post.ID))

	return response, nil
}

// GetPost retrieves a post by ID
func (s *Service) GetPost(ctx context.Context, id int) (*dto.PostResponse, error) {
	ctx, span := tracing.StartSpan(ctx, "blog-service", "get_post")
	defer span.End()

	tracing.AddSpanAttributes(ctx, attribute.Int("blog.post_id", id))

	// Get post from database
	post, err := s.repo.GetPostByID(ctx, id)
	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Get category name if available
	categoryName := ""
	if post.CategoryID > 0 {
		category, err := s.repo.GetCategoryByID(ctx, post.CategoryID)
		if err == nil && category != nil {
			categoryName = category.Name
		}
	}

	// Prepare response
	response := &dto.PostResponse{
		ID:            post.ID,
		Title:         post.Title,
		Slug:          post.Slug,
		Content:       post.Content,
		AuthorID:      post.AuthorID,
		CategoryID:    post.CategoryID,
		CategoryName:  categoryName,
		Status:        post.Status,
		FeaturedImage: post.FeaturedImage,
		PublishedAt:   post.PublishedAt,
		CreatedAt:     post.CreatedAt,
		UpdatedAt:     post.UpdatedAt,
	}

	tracing.SetSpanStatus(ctx, codes.Ok, "Post retrieved successfully")
	return response, nil
}

// GetPostBySlug retrieves a post by slug
func (s *Service) GetPostBySlug(ctx context.Context, slug string) (*dto.PostResponse, error) {
	ctx, span := tracing.StartSpan(ctx, "blog-service", "get_post_by_slug")
	defer span.End()

	tracing.AddSpanAttributes(ctx, attribute.String("blog.slug", slug))

	// Get post from database
	post, err := s.repo.GetPostBySlug(ctx, slug)
	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Get category name if available
	categoryName := ""
	if post.CategoryID > 0 {
		category, err := s.repo.GetCategoryByID(ctx, post.CategoryID)
		if err == nil && category != nil {
			categoryName = category.Name
		}
	}

	// Prepare response
	response := &dto.PostResponse{
		ID:            post.ID,
		Title:         post.Title,
		Slug:          post.Slug,
		Content:       post.Content,
		AuthorID:      post.AuthorID,
		CategoryID:    post.CategoryID,
		CategoryName:  categoryName,
		Status:        post.Status,
		FeaturedImage: post.FeaturedImage,
		PublishedAt:   post.PublishedAt,
		CreatedAt:     post.CreatedAt,
		UpdatedAt:     post.UpdatedAt,
	}

	tracing.SetSpanStatus(ctx, codes.Ok, "Post retrieved successfully")
	return response, nil
}

// UpdatePost updates an existing post
func (s *Service) UpdatePost(ctx context.Context, id int, req dto.UpdatePostRequest) (*dto.PostResponse, error) {
	ctx, span := tracing.StartSpan(ctx, "blog-service", "update_post")
	defer span.End()

	tracing.AddSpanAttributes(ctx, attribute.Int("blog.post_id", id))

	// Get user ID from context
	userID, ok := ctx.Value("user_id").(int)
	if !ok {
		tracing.SetSpanStatus(ctx, codes.Error, "User not authenticated")
		return nil, internal.ErrNotAuthorized
	}

	// Get existing post
	post, err := s.repo.GetPostByID(ctx, id)
	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Check ownership
	if post.AuthorID != userID {
		// Check if user has admin role (to be implemented)
		isAdmin := false
		if !isAdmin {
			tracing.SetSpanStatus(ctx, codes.Error, "Not authorized")
			return nil, internal.ErrNotAuthorized
		}
	}

	// Update fields if provided
	if req.Title != nil {
		post.Title = *req.Title
	}
	if req.Content != nil {
		post.Content = *req.Content
	}
	if req.CategoryID != nil {
		post.CategoryID = *req.CategoryID
	}
	if req.Status != nil {
		post.Status = *req.Status
	}
	if req.FeaturedImage != nil {
		post.FeaturedImage = *req.FeaturedImage
	}

	// Handle slug update
	if req.Slug != nil && *req.Slug != post.Slug {
		newSlug := slug.Make(*req.Slug)
		if newSlug == "" {
			return nil, internal.ErrInvalidSlug
		}

		// Check if slug is already in use
		existingPost, err := s.repo.GetPostBySlug(ctx, newSlug)
		if err != nil && !errors.Is(err, internal.ErrPostNotFound) {
			tracing.RecordError(ctx, err)
			return nil, err
		}
		
		if existingPost != nil && existingPost.ID != id {
			tracing.SetSpanStatus(ctx, codes.Error, "Duplicate slug")
			return nil, internal.ErrDuplicateSlug
		}

		post.Slug = newSlug
	}

	// Handle publish status change
	if req.PublishNow != nil && *req.PublishNow && post.Status == "published" && post.PublishedAt == nil {
		now := time.Now()
		post.PublishedAt = &now
	}

	post.UpdatedAt = time.Now()

	// Save to database
	if err := s.repo.UpdatePost(ctx, post); err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Get category name if available
	categoryName := ""
	if post.CategoryID > 0 {
		category, err := s.repo.GetCategoryByID(ctx, post.CategoryID)
		if err == nil && category != nil {
			categoryName = category.Name
		}
	}

	// Prepare response
	response := &dto.PostResponse{
		ID:            post.ID,
		Title:         post.Title,
		Slug:          post.Slug,
		Content:       post.Content,
		AuthorID:      post.AuthorID,
		CategoryID:    post.CategoryID,
		CategoryName:  categoryName,
		Status:        post.Status,
		FeaturedImage: post.FeaturedImage,
		PublishedAt:   post.PublishedAt,
		CreatedAt:     post.CreatedAt,
		UpdatedAt:     post.UpdatedAt,
	}

	tracing.SetSpanStatus(ctx, codes.Ok, "Post updated successfully")
	return response, nil
}

// DeletePost deletes a post
func (s *Service) DeletePost(ctx context.Context, id int) error {
	ctx, span := tracing.StartSpan(ctx, "blog-service", "delete_post")
	defer span.End()

	tracing.AddSpanAttributes(ctx, attribute.Int("blog.post_id", id))

	// Get user ID from context
	userID, ok := ctx.Value("user_id").(int)
	if !ok {
		tracing.SetSpanStatus(ctx, codes.Error, "User not authenticated")
		return internal.ErrNotAuthorized
	}

	// Get existing post
	post, err := s.repo.GetPostByID(ctx, id)
	if err != nil {
		tracing.RecordError(ctx, err)
		return err
	}

	// Check ownership
	if post.AuthorID != userID {
		// Check if user has admin role (to be implemented)
		isAdmin := false
		if !isAdmin {
			tracing.SetSpanStatus(ctx, codes.Error, "Not authorized")
			return internal.ErrNotAuthorized
		}
	}

	// Delete from database
	if err := s.repo.DeletePost(ctx, id); err != nil {
		tracing.RecordError(ctx, err)
		return err
	}

	tracing.SetSpanStatus(ctx, codes.Ok, "Post deleted successfully")
	return nil
}

// ListPosts lists posts with filtering
func (s *Service) ListPosts(ctx context.Context, filter dto.PostFilter) (*dto.PostListResponse, error) {
	ctx, span := tracing.StartSpan(ctx, "blog-service", "list_posts")
	defer span.End()

	// Set default values
	if filter.Page <= 0 {
		filter.Page = 1
	}
	if filter.PerPage <= 0 {
		filter.PerPage = s.config.PostsPerPage
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Int("blog.page", filter.Page),
		attribute.Int("blog.per_page", filter.PerPage),
		attribute.String("blog.status_filter", filter.Status),
		attribute.String("blog.search", filter.Search),
	)

	// Get posts from database
	posts, totalCount, err := s.repo.ListPosts(ctx, filter)
	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Calculate pagination values
	totalPages := int(math.Ceil(float64(totalCount) / float64(filter.PerPage)))

	// Convert to response DTOs
	postResponses := make([]*dto.PostResponse, 0, len(posts))
	
	// Get category names
	categoryMap := make(map[int]string)
	
	for _, post := range posts {
		// Get category name if not already in map
		categoryName := ""
		if post.CategoryID > 0 {
			if name, ok := categoryMap[post.CategoryID]; ok {
				categoryName = name
			} else {
				category, err := s.repo.GetCategoryByID(ctx, post.CategoryID)
				if err == nil && category != nil {
					categoryName = category.Name
					categoryMap[post.CategoryID] = categoryName
				}
			}
		}
		
		postResponses = append(postResponses, &dto.PostResponse{
			ID:            post.ID,
			Title:         post.Title,
			Slug:          post.Slug,
			Content:       post.Content,
			AuthorID:      post.AuthorID,
			CategoryID:    post.CategoryID,
			CategoryName:  categoryName,
			Status:        post.Status,
			FeaturedImage: post.FeaturedImage,
			PublishedAt:   post.PublishedAt,
			CreatedAt:     post.CreatedAt,
			UpdatedAt:     post.UpdatedAt,
		})
	}

	response := &dto.PostListResponse{
		Posts:      postResponses,
		TotalCount: totalCount,
		Page:       filter.Page,
		PerPage:    filter.PerPage,
		TotalPages: totalPages,
	}

	tracing.SetSpanStatus(ctx, codes.Ok, "Posts listed successfully")
	tracing.AddSpanAttributes(ctx, 
		attribute.Int("blog.total_count", totalCount),
		attribute.Int("blog.total_pages", totalPages),
	)

	return response, nil
}

// Implement other service methods (Category and Comment operations) following the same pattern
// ...

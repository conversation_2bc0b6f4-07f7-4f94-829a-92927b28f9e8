# Air configuration for blog module
root = "."
tmp_dir = "tmp"
[build]
  cmd = "go build -gcflags='all=-N -l' -o ./tmp/main cmd/main.go"
  bin = "./tmp/main"
  full_bin = "dlv exec --accept-multiclient --log --headless --continue --listen :49043 --api-version 2 ./tmp/main"
  delay = 1000
  exclude_dir = ["tmp", "vendor", ".git"]
  include_ext = ["go", "tpl", "tmpl", "html"]
  exclude_regex = ["_test.go"]
[color]
  main = "magenta"
  watcher = "cyan"
  build = "yellow"
  runner = "green"
[log]
  time = true
[misc]
  clean_on_exit = true 
// Navigate to your project root directory
cd /home/<USER>/Desktop/Workspace/Webnew/wn-backend-v2

// Run all tests in the mysql package
go test ./modules/blog/repository/mysql
go test ./repository/mysql

// Run with verbose output to see each test case
go test -v ./modules/blog/repository/mysql

// Run a specific test suite
go test -v ./modules/blog/repository/mysql -run TestTagRepositorySuite

// Run a specific test method within the suite
go test -v ./modules/blog/repository/mysql -run TestTagRepositorySuite/TestGetTagsWithPostCount

// Generate test coverage report
go test -cover ./modules/blog/repository/mysql
go test -coverprofile=coverage.out ./modules/blog/repository/mysql
go tool cover -html=coverage.out -o coverage.html
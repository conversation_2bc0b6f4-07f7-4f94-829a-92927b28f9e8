openapi: 3.1.0
info:
  title: Blog Module API
  description: API specification for the Blog module
  version: 1.0.0
  contact:
    name: Web New Team
servers:
  - url: http://wn-api.local
    description: Blog API endpoint

security:
  - bearerAuth: []

tags:
  - name: Posts
    description: Blog post management endpoints
  - name: Categories
    description: Blog category management endpoints
  - name: Tags
    description: Blog tag management endpoints

paths:
  /api/v1/blog/posts:
    get:
      summary: List posts
      description: Retrieve a paginated list of blog posts
      tags:
        - Posts
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - name: cursor
          in: query
          description: Cursor for pagination
          schema:
            type: string
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
        - name: status
          in: query
          description: Filter by post status
          schema:
            type: string
            enum: [draft, pending, published, private, trash]
        - name: visibility
          in: query
          description: Filter by post visibility
          schema:
            type: string
            enum: [public, private, password_protected]
        - name: author_id
          in: query
          description: Filter by author ID
          schema:
            type: integer
        - name: category_id
          in: query
          description: Filter by category ID
          schema:
            type: integer
        - name: tag_id
          in: query
          description: Filter by tag ID
          schema:
            type: integer
        - name: search
          in: query
          description: Search query
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostListResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
    post:
      summary: Create a post
      description: Create a new blog post
      tags:
        - Posts
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostCreate'
      responses:
        '201':
          description: Post created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'

  /api/v1/blog/posts/{postId}:
    get:
      summary: Get post
      description: Retrieve details of a specific blog post
      tags:
        - Posts
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/postIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
    put:
      summary: Update post
      description: Update an existing blog post
      tags:
        - Posts
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/postIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostUpdate'
      responses:
        '200':
          description: Post updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      summary: Delete post
      description: Delete a blog post
      tags:
        - Posts
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/postIdParam'
      responses:
        '204':
          description: Post deleted successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

  /api/v1/blog/posts/{postId}/status:
    patch:
      summary: Update post status
      description: Update the status of a blog post
      tags:
        - Posts
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/postIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - status
              properties:
                status:
                  type: string
                  enum: [draft, pending, published, private, trash]
      responses:
        '200':
          description: Post status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /api/v1/blog/categories:
    get:
      summary: List categories
      description: Retrieve a list of blog categories
      tags:
        - Categories
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - name: cursor
          in: query
          description: Cursor for pagination
          schema:
            type: string
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
        - name: tree
          in: query
          description: Return categories in tree structure
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategoryListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
    post:
      summary: Create category
      description: Create a new blog category
      tags:
        - Categories
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategoryCreate'
      responses:
        '201':
          description: Category created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategoryResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'

  /api/v1/blog/categories/{categoryId}:
    get:
      summary: Get category
      description: Retrieve details of a specific blog category
      tags:
        - Categories
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/categoryIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategoryResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
    put:
      summary: Update category
      description: Update an existing blog category
      tags:
        - Categories
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/categoryIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategoryUpdate'
      responses:
        '200':
          description: Category updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategoryResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      summary: Delete category
      description: Delete a blog category
      tags:
        - Categories
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/categoryIdParam'
      responses:
        '204':
          description: Category deleted successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

  /api/v1/blog/tags:
    get:
      summary: List tags
      description: Retrieve a list of blog tags
      tags:
        - Tags
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - name: cursor
          in: query
          description: Cursor for pagination
          schema:
            type: string
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
        - name: search
          in: query
          description: Search by tag name
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TagListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
    post:
      summary: Create tag
      description: Create a new blog tag
      tags:
        - Tags
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TagCreate'
      responses:
        '201':
          description: Tag created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TagResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'

  /api/v1/blog/tags/{tagId}:
    get:
      summary: Get tag
      description: Retrieve details of a specific blog tag
      tags:
        - Tags
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/tagIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TagResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
    put:
      summary: Update tag
      description: Update an existing blog tag
      tags:
        - Tags
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/tagIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TagUpdate'
      responses:
        '200':
          description: Tag updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TagResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
    delete:
      summary: Delete tag
      description: Delete a blog tag
      tags:
        - Tags
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/tenantIdParam'
        - $ref: '#/components/parameters/tagIdParam'
      responses:
        '204':
          description: Tag deleted successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  parameters:
    tenantIdParam:
      name: X-Tenant-ID
      in: header
      required: true
      description: ID of the tenant
      schema:
        type: integer
        format: int64
    postIdParam:
      name: postId
      in: path
      required: true
      description: ID of the post
      schema:
        type: integer
        format: int64
    categoryIdParam:
      name: categoryId
      in: path
      required: true
      description: ID of the category
      schema:
        type: integer
        format: int64
    tagIdParam:
      name: tagId
      in: path
      required: true
      description: ID of the tag
      schema:
        type: integer
        format: int64

  responses:
    BadRequest:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    Unauthorized:
      description: Unauthorized - Token is missing or invalid
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: object
                properties:
                  code:
                    type: integer
                    example: 401
                  message:
                    type: string
                    example: Unauthorized
                  success:
                    type: boolean
                    example: false
                  error_code:
                    type: string
                    example: UNAUTHORIZED
                  path:
                    type: string
                    example: /api/v1/blog/posts
                  timestamp:
                    type: string
                    format: date-time
                  details:
                    type: array
                    items:
                      type: object
                      properties:
                        message:
                          type: string
                          example: Token is missing or invalid
    Forbidden:
      description: Forbidden - User does not have required permissions
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: object
                properties:
                  code:
                    type: integer
                    example: 403
                  message:
                    type: string
                    example: Forbidden
                  success:
                    type: boolean
                    example: false
                  error_code:
                    type: string
                    example: FORBIDDEN
                  path:
                    type: string
                    example: /api/v1/blog/posts
                  timestamp:
                    type: string
                    format: date-time
                  details:
                    type: array
                    items:
                      type: object
                      properties:
                        message:
                          type: string
                          example: User does not have required permissions
    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

  schemas:
    StandardResponse:
      type: object
      properties:
        status:
          type: object
          properties:
            code:
              type: integer
              description: HTTP status code
              example: 200
            message:
              type: string
              description: Status message
              example: Operation completed successfully
            success:
              type: boolean
              description: Whether the request was successful
              example: true
            error_code:
              type: string
              nullable: true
              description: Application-specific error code
              example: null
            path:
              type: string
              description: Request path
              example: /api/v1/blog/posts
            timestamp:
              type: string
              format: date-time
              description: Time when the request was processed
            details:
              type: array
              nullable: true
              description: Additional details
              items:
                type: object
              example: null
    
    Post:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Unique identifier for the post
        tenant_id:
          type: integer
          format: int64
          description: ID of the tenant this post belongs to
        title:
          type: string
          description: Post title
        slug:
          type: string
          description: URL-friendly version of the post title
        description:
          type: string
          description: Short description or excerpt of the post
        content:
          type: string
          description: Full content of the post
        featured_image:
          type: string
          description: URL to the featured image
        status:
          type: string
          enum: [draft, pending, published, private, trash]
          description: Current status of the post
        visibility:
          type: string
          enum: [public, private, password_protected]
          description: Visibility setting for the post
        comment_status:
          type: string
          enum: [open, closed]
          description: Whether comments are allowed on this post
        publish_date:
          type: string
          format: date-time
          nullable: true
          description: Date and time when the post is scheduled to be published
        created_at:
          type: string
          format: date-time
          description: Date and time when the post was created
        updated_at:
          type: string
          format: date-time
          description: Date and time when the post was last updated
        author_id:
          type: integer
          format: int64
          description: ID of the user who authored the post
        created_by:
          type: integer
          format: int64
          nullable: true
          description: ID of the user who created the post (if different from author)
        categories:
          type: array
          items:
            type: integer
            format: int64
          description: List of category IDs associated with this post
        tags:
          type: array
          items:
            type: integer
            format: int64
          description: List of tag IDs associated with this post
        author:
          $ref: '#/components/schemas/User'
          description: Details of the post author
        editor:
          $ref: '#/components/schemas/User'
          description: Details of the user who last edited the post

    PostCreate:
      type: object
      required:
        - title
      properties:
        title:
          type: string
          description: Post title (required)
        slug:
          type: string
          description: URL-friendly version of the title (auto-generated if not provided)
        description:
          type: string
          description: Short description or excerpt of the post
        content:
          type: string
          description: Full content of the post
        featured_image:
          type: string
          description: URL to the featured image
        status:
          type: string
          enum: [draft, pending, published, private, trash]
          description: Post status (defaults to draft if not specified)
          default: draft
        visibility:
          type: string
          enum: [public, private, password_protected]
          description: Post visibility setting
          default: public
        password:
          type: string
          nullable: true
          description: Password for password_protected posts
        comment_status:
          type: string
          enum: [open, closed]
          default: open
          description: Whether comments are allowed on this post
        publish_date:
          type: string
          format: date-time
          nullable: true
          description: Date and time when the post should be published
        author_id:
          type: integer
          format: int64
          description: ID of the user who authored the post
        categories:
          type: array
          items:
            type: integer
            format: int64
          description: List of category IDs to associate with this post
        tags:
          type: array
          items:
            type: integer
            format: int64
          description: List of tag IDs to associate with this post

    PostUpdate:
      type: object
      properties:
        title:
          type: string
          description: Post title
        slug:
          type: string
          description: URL-friendly version of the title
        description:
          type: string
          description: Short description or excerpt of the post
        content:
          type: string
          description: Full content of the post
        featured_image:
          type: string
          description: URL to the featured image
        status:
          type: string
          enum: [draft, pending, published, private, trash]
          description: Post status
        visibility:
          type: string
          enum: [public, private, password_protected]
          description: Post visibility setting
        password:
          type: string
          nullable: true
          description: Password for password_protected posts
        comment_status:
          type: string
          enum: [open, closed]
          description: Whether comments are allowed on this post
        publish_date:
          type: string
          format: date-time
          nullable: true
          description: Date and time when the post should be published
        author_id:
          type: integer
          format: int64
          description: ID of the user who authored the post
        categories:
          type: array
          items:
            type: integer
            format: int64
          description: List of category IDs to associate with this post
        tags:
          type: array
          items:
            type: integer
            format: int64
          description: List of tag IDs to associate with this post

    PostListResponse:
      allOf:
        - $ref: '#/components/schemas/StandardResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/Post'
            meta:
              type: object
              properties:
                next_cursor:
                  type: string
                  nullable: true
                has_more:
                  type: boolean

    PostResponse:
      allOf:
        - $ref: '#/components/schemas/StandardResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/Post'

    Category:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Unique identifier for the category
        tenant_id:
          type: integer
          format: int64
          description: ID of the tenant this category belongs to
        parent_id:
          type: integer
          format: int64
          nullable: true
          description: ID of the parent category (null for top-level categories)
        name:
          type: string
          description: Category name
        slug:
          type: string
          description: URL-friendly version of the category name
        description:
          type: string
          description: Category description
        featured_image:
          type: string
          description: URL to the featured image
        lft:
          type: integer
          description: Left value in nested set model
        rgt:
          type: integer
          description: Right value in nested set model
        depth:
          type: integer
          description: Depth level in the category tree
        position:
          type: integer
          description: Position among siblings
        is_active:
          type: boolean
          description: Whether the category is active
        is_featured:
          type: boolean
          description: Whether the category is featured
        meta_title:
          type: string
          description: SEO meta title
        meta_description:
          type: string
          description: SEO meta description
        created_at:
          type: string
          format: date-time
          description: Date and time when the category was created
        updated_at:
          type: string
          format: date-time
          description: Date and time when the category was last updated
        created_by:
          type: integer
          format: int64
          description: ID of the user who created the category
        updated_by:
          type: integer
          format: int64
          description: ID of the user who last updated the category
        children:
          type: array
          description: Child categories (for tree structure)
          items:
            $ref: '#/components/schemas/Category'
        post_count:
          type: integer

    CategoryCreate:
      type: object
      required:
        - name
      properties:
        parent_id:
          type: integer
          format: int64
          nullable: true
        name:
          type: string
        slug:
          type: string
        description:
          type: string
        featured_image:
          type: string
        position:
          type: integer
        is_active:
          type: boolean
          default: true
        is_featured:
          type: boolean
          default: false
        meta_title:
          type: string
        meta_description:
          type: string

    CategoryUpdate:
      type: object
      properties:
        parent_id:
          type: integer
          format: int64
          nullable: true
        name:
          type: string
        slug:
          type: string
        description:
          type: string
        featured_image:
          type: string
        position:
          type: integer
        is_active:
          type: boolean
        is_featured:
          type: boolean
        meta_title:
          type: string
        meta_description:
          type: string

    CategoryListResponse:
      allOf:
        - $ref: '#/components/schemas/StandardResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/Category'
            meta:
              type: object
              properties:
                next_cursor:
                  type: string
                  nullable: true
                has_more:
                  type: boolean

    CategoryResponse:
      allOf:
        - $ref: '#/components/schemas/StandardResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/Category'

    Tag:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Unique identifier for the tag
        tenant_id:
          type: integer
          format: int64
          description: ID of the tenant this tag belongs to
        name:
          type: string
          description: Tag name
        slug:
          type: string
          description: URL-friendly version of the tag name
        description:
          type: string
          description: Tag description
        is_active:
          type: boolean
          description: Whether the tag is active
        created_at:
          type: string
          format: date-time
          description: Date and time when the tag was created
        updated_at:
          type: string
          format: date-time
          description: Date and time when the tag was last updated
        post_count:
          type: integer
          description: Number of posts associated with this tag

    TagCreate:
      type: object
      required:
        - name
      properties:
        name:
          type: string
        slug:
          type: string
        description:
          type: string
        is_active:
          type: boolean
          default: true

    TagUpdate:
      type: object
      properties:
        name:
          type: string
        slug:
          type: string
        description:
          type: string
        is_active:
          type: boolean

    TagListResponse:
      allOf:
        - $ref: '#/components/schemas/StandardResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/Tag'
            meta:
              type: object
              properties:
                next_cursor:
                  type: string
                  nullable: true
                has_more:
                  type: boolean

    TagResponse:
      allOf:
        - $ref: '#/components/schemas/StandardResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/Tag'

    User:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Unique identifier for the user
        username:
          type: string
          description: User's username
        email:
          type: string
          description: User's email address
        first_name:
          type: string
          description: User's first name
        last_name:
          type: string
          description: User's last name
        avatar_url:
          type: string
          description: URL to the user's avatar image

    Error:
      type: object
      properties:
        status:
          type: object
          properties:
            code:
              type: integer
              description: HTTP status code
              example: 400
            message:
              type: string
              description: Error message
              example: Bad Request
            success:
              type: boolean
              description: Whether the request was successful
              example: false
            error_code:
              type: string
              description: Application-specific error code
              example: VALIDATION_ERROR
            path:
              type: string
              description: Request path
              example: /api/v1/blog/posts
            timestamp:
              type: string
              format: date-time
              description: Time when the error occurred
            details:
              type: array
              description: Detailed error information
              items:
                type: object
                properties:
                  field:
                    type: string
                    description: Field with error (for validation errors)
                    example: title
                  message:
                    type: string
                    description: Detailed error message
                    example: Title is required
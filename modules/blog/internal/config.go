package internal

import (
	"fmt"
	"log"
	"os"

	"github.com/caarlos0/env/v11"
	"github.com/joho/godotenv"
)

// BlogConfig contains configuration for the blog module
type BlogConfig struct {
	PostsPerPage    int    `env:"POSTS_PER_PAGE" envDefault:"10"`
	EnableComments  bool   `env:"ENABLE_COMMENTS" envDefault:"true"`
	DefaultCategory string `env:"DEFAULT_CATEGORY" envDefault:"uncategorized"`
	Message         string `env:"MESSAGE" envDefault:"Welcome to our blog!"`
}

// LoadBlogConfig loads configuration from environment variables
func LoadBlogConfig() (*BlogConfig, error) {
	// Load .env file if it exists
	if _, err := os.Stat(".env"); err == nil {
		if err := godotenv.Load(); err != nil {
			log.Printf("Warning: could not load .env file: %v", err)
		}
	}

	// Initialize default config
	cfg := &BlogConfig{
		PostsPerPage:    10,
		EnableComments:  true,
		DefaultCategory: "uncategorized",
		Message:         "Welcome to our blog!",
	}

	// Parse configuration from environment variables with prefix BLOG_
	opts := env.Options{
		Prefix: "BLOG_",
	}
	if err := env.ParseWithOptions(cfg, opts); err != nil {
		return nil, fmt.Errorf("error parsing blog config from environment: %w", err)
	}

	return cfg, nil
}

package internal

import (
	"context"
	"net/http"
	"time"

	"wnapi/modules/blog/dto"
)

// Post represents a blog post entity
type Post struct {
	ID            int        `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Title         string     `gorm:"column:title;not null" json:"title"`
	Slug          string     `gorm:"column:slug;uniqueIndex;not null" json:"slug"`
	Content       string     `gorm:"column:content;type:text" json:"content"`
	AuthorID      int        `gorm:"column:author_id;not null" json:"author_id"`
	CategoryID    int        `gorm:"column:category_id" json:"category_id"`
	Status        string     `gorm:"column:status;default:draft" json:"status"`
	FeaturedImage string     `gorm:"column:featured_image" json:"featured_image"`
	PublishedAt   *time.Time `gorm:"column:published_at" json:"published_at"`
	CreatedAt     time.Time  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// Category represents a blog category
type Category struct {
	ID          int       `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name        string    `gorm:"column:name;not null" json:"name"`
	Slug        string    `gorm:"column:slug;uniqueIndex;not null" json:"slug"`
	Description string    `gorm:"column:description" json:"description"`
	CreatedAt   time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// Comment represents a blog comment
type Comment struct {
	ID        int       `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	PostID    int       `gorm:"column:post_id;not null" json:"post_id"`
	AuthorID  int       `gorm:"column:author_id" json:"author_id"`
	ParentID  *int      `gorm:"column:parent_id" json:"parent_id"`
	Content   string    `gorm:"column:content;not null" json:"content"`
	Status    string    `gorm:"column:status;default:pending" json:"status"`
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// ServiceError defines service-specific errors
type ServiceError string

const (
	// ErrPostNotFound is returned when a post is not found
	ErrPostNotFound ServiceError = "post_not_found"
	// ErrCategoryNotFound is returned when a category is not found
	ErrCategoryNotFound ServiceError = "category_not_found"
	// ErrInvalidSlug is returned when a slug is invalid
	ErrInvalidSlug ServiceError = "invalid_slug"
	// ErrDuplicateSlug is returned when a slug already exists
	ErrDuplicateSlug ServiceError = "duplicate_slug"
	// ErrNotAuthorized is returned when a user is not authorized
	ErrNotAuthorized ServiceError = "not_authorized"
)

// ErrorResponse defines an error response structure
type ErrorResponse struct {
	StatusCode int    // HTTP status code
	Message    string // User-friendly error message
	ErrorCode  string // Internal error code
}

// ErrorMap maps ServiceErrors to appropriate HTTP responses
var ErrorMap = map[ServiceError]ErrorResponse{
	ErrPostNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Post không tồn tại",
		ErrorCode:  "POST_NOT_FOUND",
	},
	ErrCategoryNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Danh mục không tồn tại",
		ErrorCode:  "CATEGORY_NOT_FOUND",
	},
	ErrInvalidSlug: {
		StatusCode: http.StatusBadRequest,
		Message:    "Slug không hợp lệ",
		ErrorCode:  "INVALID_SLUG",
	},
	ErrDuplicateSlug: {
		StatusCode: http.StatusConflict,
		Message:    "Slug đã tồn tại",
		ErrorCode:  "DUPLICATE_SLUG",
	},
	ErrNotAuthorized: {
		StatusCode: http.StatusForbidden,
		Message:    "Bạn không có quyền thực hiện hành động này",
		ErrorCode:  "NOT_AUTHORIZED",
	},
}

func (e ServiceError) Error() string {
	return string(e)
}

// GetErrorResponse returns the appropriate error response for a given error
func GetErrorResponse(err error) ErrorResponse {
	if serviceErr, ok := err.(ServiceError); ok {
		if resp, exists := ErrorMap[serviceErr]; exists {
			return resp
		}
	}

	// Default to internal error
	return ErrorResponse{
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi hệ thống",
		ErrorCode:  "INTERNAL_ERROR",
	}
}

// Repository defines the interface for data access
type Repository interface {
	// Post methods
	CreatePost(ctx context.Context, post *Post) error
	GetPostByID(ctx context.Context, id int) (*Post, error)
	GetPostBySlug(ctx context.Context, slug string) (*Post, error)
	UpdatePost(ctx context.Context, post *Post) error
	DeletePost(ctx context.Context, id int) error
	ListPosts(ctx context.Context, filter dto.PostFilter) ([]*Post, int, error)

	// Category methods
	CreateCategory(ctx context.Context, category *Category) error
	GetCategoryByID(ctx context.Context, id int) (*Category, error)
	GetCategoryBySlug(ctx context.Context, slug string) (*Category, error)
	UpdateCategory(ctx context.Context, category *Category) error
	DeleteCategory(ctx context.Context, id int) error
	ListCategories(ctx context.Context) ([]*Category, error)

	// Comment methods
	CreateComment(ctx context.Context, comment *Comment) error
	GetCommentsByPostID(ctx context.Context, postID int) ([]*Comment, error)
	UpdateComment(ctx context.Context, comment *Comment) error
	DeleteComment(ctx context.Context, id int) error
}

// BlogService defines the interface for the blog service
type BlogService interface {
	// Post methods
	CreatePost(ctx context.Context, req dto.CreatePostRequest) (*dto.PostResponse, error)
	GetPost(ctx context.Context, id int) (*dto.PostResponse, error)
	GetPostBySlug(ctx context.Context, slug string) (*dto.PostResponse, error)
	UpdatePost(ctx context.Context, id int, req dto.UpdatePostRequest) (*dto.PostResponse, error)
	DeletePost(ctx context.Context, id int) error
	ListPosts(ctx context.Context, filter dto.PostFilter) (*dto.PostListResponse, error)

	// Category methods
	CreateCategory(ctx context.Context, req dto.CreateCategoryRequest) (*dto.CategoryResponse, error)
	GetCategory(ctx context.Context, id int) (*dto.CategoryResponse, error)
	UpdateCategory(ctx context.Context, id int, req dto.UpdateCategoryRequest) (*dto.CategoryResponse, error)
	DeleteCategory(ctx context.Context, id int) error
	ListCategories(ctx context.Context) (*dto.CategoryListResponse, error)

	// Comment methods
	CreateComment(ctx context.Context, req dto.CreateCommentRequest) (*dto.CommentResponse, error)
	GetCommentsByPost(ctx context.Context, postID int) (*dto.CommentListResponse, error)
	UpdateComment(ctx context.Context, id int, req dto.UpdateCommentRequest) (*dto.CommentResponse, error)
	DeleteComment(ctx context.Context, id int) error
}

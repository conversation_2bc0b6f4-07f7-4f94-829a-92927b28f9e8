package dto

import "time"

// CreateCategoryRequest contains the data needed to create a blog category
type CreateCategoryRequest struct {
	Name        string `json:"name" binding:"required"`
	Slug        string `json:"slug"`
	Description string `json:"description"`
}

// UpdateCategoryRequest contains the data needed to update a blog category
type UpdateCategoryRequest struct {
	Name        *string `json:"name"`
	Slug        *string `json:"slug"`
	Description *string `json:"description"`
}

// CategoryResponse contains the response data for a blog category
type CategoryResponse struct {
	ID          int       `json:"id"`
	Name        string    `json:"name"`
	Slug        string    `json:"slug"`
	Description string    `json:"description"`
	PostCount   int       `json:"post_count,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// CategoryListResponse contains the response data for a list of blog categories
type CategoryListResponse struct {
	Categories []*CategoryResponse `json:"categories"`
	TotalCount int                 `json:"total_count"`
}

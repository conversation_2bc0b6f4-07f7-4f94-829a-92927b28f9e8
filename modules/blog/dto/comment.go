package dto

import "time"

// CreateCommentRequest contains the data needed to create a blog comment
type CreateCommentRequest struct {
	PostID   int    `json:"post_id" binding:"required"`
	ParentID *int   `json:"parent_id"`
	Content  string `json:"content" binding:"required"`
}

// UpdateCommentRequest contains the data needed to update a blog comment
type UpdateCommentRequest struct {
	Content *string `json:"content"`
	Status  *string `json:"status" binding:"omitempty,oneof=pending approved rejected"`
}

// CommentResponse contains the response data for a blog comment
type CommentResponse struct {
	ID        int       `json:"id"`
	PostID    int       `json:"post_id"`
	AuthorID  int       `json:"author_id"`
	ParentID  *int      `json:"parent_id"`
	Content   string    `json:"content"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	// Optional fields for API responses
	AuthorName string             `json:"author_name,omitempty"`
	Replies    []*CommentResponse `json:"replies,omitempty"`
}

// CommentListResponse contains the response data for a list of blog comments
type CommentListResponse struct {
	Comments   []*CommentResponse `json:"comments"`
	TotalCount int                `json:"total_count"`
}

package dto

import "time"

// CreatePostRequest contains the data needed to create a blog post
type CreatePostRequest struct {
	Title        string `json:"title" binding:"required"`
	Content      string `json:"content" binding:"required"`
	Slug         string `json:"slug"`
	CategoryID   int    `json:"category_id"`
	Status       string `json:"status" binding:"oneof=draft published private"`
	FeaturedImage string `json:"featured_image"`
	PublishNow   bool   `json:"publish_now"`
}

// UpdatePostRequest contains the data needed to update a blog post
type UpdatePostRequest struct {
	Title        *string `json:"title"`
	Content      *string `json:"content"`
	Slug         *string `json:"slug"`
	CategoryID   *int    `json:"category_id"`
	Status       *string `json:"status" binding:"omitempty,oneof=draft published private"`
	FeaturedImage *string `json:"featured_image"`
	PublishNow   *bool   `json:"publish_now"`
}

// PostResponse contains the response data for a blog post
type PostResponse struct {
	ID            int        `json:"id"`
	Title         string     `json:"title"`
	Slug          string     `json:"slug"`
	Content       string     `json:"content"`
	AuthorID      int        `json:"author_id"`
	CategoryID    int        `json:"category_id"`
	CategoryName  string     `json:"category_name,omitempty"`
	Status        string     `json:"status"`
	FeaturedImage string     `json:"featured_image"`
	PublishedAt   *time.Time `json:"published_at"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
}

// PostFilter contains the filter parameters for listing posts
type PostFilter struct {
	Page       int    `form:"page" binding:"omitempty,min=1"`
	PerPage    int    `form:"per_page" binding:"omitempty,min=1,max=100"`
	CategoryID int    `form:"category_id" binding:"omitempty,min=1"`
	Status     string `form:"status" binding:"omitempty,oneof=draft published private all"`
	Search     string `form:"search"`
	AuthorID   int    `form:"author_id" binding:"omitempty,min=1"`
}

// PostListResponse contains the response data for a list of blog posts
type PostListResponse struct {
	Posts      []*PostResponse `json:"posts"`
	TotalCount int             `json:"total_count"`
	Page       int             `json:"page"`
	PerPage    int             `json:"per_page"`
	TotalPages int             `json:"total_pages"`
}

package handlers

import (
	"strconv"

	"wnapi/internal/pkg/response"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/blog/dto"
	"wnapi/modules/blog/internal"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
)

// PostHandler handles HTTP requests for blog posts
type PostHandler struct {
	blogService internal.BlogService
}

// NewPostHandler creates a new post handler
func NewPostHandler(blogService internal.BlogService) *PostHandler {
	return &PostHandler{blogService: blogService}
}

// CreatePost handles the creation of a new blog post
func (h *PostHandler) CreatePost(c *gin.Context) {
	// Start span for the operation
	ctx, span := tracing.StartGinSpan(c, "blog", "create_post")
	defer span.End()

	var req dto.CreatePostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		tracing.RecordGinError(c, err)
		tracing.AddGinSpanAttributes(c, attribute.String("blog.error_type", "invalid_request"))

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.BadRequest(c, "Định dạng yêu cầu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	resp, err := h.blogService.CreatePost(ctx, req)
	if err != nil {
		tracing.RecordGinError(c, err)

		errResp := internal.GetErrorResponse(err)
		tracing.AddGinSpanAttributes(c, attribute.String("blog.error_type", errResp.ErrorCode))

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, details)
		return
	}

	tracing.SetSpanStatus(ctx, codes.Ok, "Post created successfully")
	tracing.AddGinSpanAttributes(c,
		attribute.Int("blog.post_id", resp.ID),
		attribute.Bool("blog.success", true),
	)
	tracing.AddGinSpanEvent(c, "post_created")

	response.Created(c, resp, nil)
}

// GetPost handles retrieving a blog post by ID
func (h *PostHandler) GetPost(c *gin.Context) {
	ctx, span := tracing.StartGinSpan(c, "blog", "get_post")
	defer span.End()

	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		tracing.RecordGinError(c, err)
		response.BadRequest(c, "ID không hợp lệ", "INVALID_ID", nil)
		return
	}

	tracing.AddGinSpanAttributes(c, attribute.Int("blog.post_id", id))

	resp, err := h.blogService.GetPost(ctx, id)
	if err != nil {
		tracing.RecordGinError(c, err)

		errResp := internal.GetErrorResponse(err)
		tracing.AddGinSpanAttributes(c, attribute.String("blog.error_type", errResp.ErrorCode))

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, details)
		return
	}

	tracing.SetSpanStatus(ctx, codes.Ok, "Post retrieved successfully")
	response.Success(c, resp, nil)
}

// GetPostBySlug handles retrieving a blog post by slug
func (h *PostHandler) GetPostBySlug(c *gin.Context) {
	ctx, span := tracing.StartGinSpan(c, "blog", "get_post_by_slug")
	defer span.End()

	slug := c.Param("slug")
	tracing.AddGinSpanAttributes(c, attribute.String("blog.slug", slug))

	resp, err := h.blogService.GetPostBySlug(ctx, slug)
	if err != nil {
		tracing.RecordGinError(c, err)

		errResp := internal.GetErrorResponse(err)
		tracing.AddGinSpanAttributes(c, attribute.String("blog.error_type", errResp.ErrorCode))

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, details)
		return
	}

	tracing.SetSpanStatus(ctx, codes.Ok, "Post retrieved successfully")
	response.Success(c, resp, nil)
}

// UpdatePost handles updating an existing blog post
func (h *PostHandler) UpdatePost(c *gin.Context) {
	ctx, span := tracing.StartGinSpan(c, "blog", "update_post")
	defer span.End()

	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		tracing.RecordGinError(c, err)
		response.BadRequest(c, "ID không hợp lệ", "INVALID_ID", nil)
		return
	}

	tracing.AddGinSpanAttributes(c, attribute.Int("blog.post_id", id))

	var req dto.UpdatePostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		tracing.RecordGinError(c, err)
		tracing.AddGinSpanAttributes(c, attribute.String("blog.error_type", "invalid_request"))

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.BadRequest(c, "Định dạng yêu cầu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	resp, err := h.blogService.UpdatePost(ctx, id, req)
	if err != nil {
		tracing.RecordGinError(c, err)

		errResp := internal.GetErrorResponse(err)
		tracing.AddGinSpanAttributes(c, attribute.String("blog.error_type", errResp.ErrorCode))

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, details)
		return
	}

	tracing.SetSpanStatus(ctx, codes.Ok, "Post updated successfully")
	tracing.AddGinSpanEvent(c, "post_updated")

	response.Success(c, resp, nil)
}

// DeletePost handles deleting a blog post
func (h *PostHandler) DeletePost(c *gin.Context) {
	ctx, span := tracing.StartGinSpan(c, "blog", "delete_post")
	defer span.End()

	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		tracing.RecordGinError(c, err)
		response.BadRequest(c, "ID không hợp lệ", "INVALID_ID", nil)
		return
	}

	tracing.AddGinSpanAttributes(c, attribute.Int("blog.post_id", id))

	err = h.blogService.DeletePost(ctx, id)
	if err != nil {
		tracing.RecordGinError(c, err)

		errResp := internal.GetErrorResponse(err)
		tracing.AddGinSpanAttributes(c, attribute.String("blog.error_type", errResp.ErrorCode))

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, details)
		return
	}

	tracing.SetSpanStatus(ctx, codes.Ok, "Post deleted successfully")
	tracing.AddGinSpanEvent(c, "post_deleted")

	response.Success(c, gin.H{"message": "Bài viết đã được xóa thành công"}, nil)
}

// ListPosts handles listing blog posts with filters
func (h *PostHandler) ListPosts(c *gin.Context) {
	ctx, span := tracing.StartGinSpan(c, "blog", "list_posts")
	defer span.End()

	var filter dto.PostFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		tracing.RecordGinError(c, err)
		tracing.AddGinSpanAttributes(c, attribute.String("blog.error_type", "invalid_request"))

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.BadRequest(c, "Tham số tìm kiếm không hợp lệ", "INVALID_FILTER", details)
		return
	}

	tracing.AddGinSpanAttributes(c,
		attribute.Int("blog.page", filter.Page),
		attribute.Int("blog.per_page", filter.PerPage),
		attribute.String("blog.search", filter.Search),
	)

	resp, err := h.blogService.ListPosts(ctx, filter)
	if err != nil {
		tracing.RecordGinError(c, err)

		errResp := internal.GetErrorResponse(err)
		tracing.AddGinSpanAttributes(c, attribute.String("blog.error_type", errResp.ErrorCode))

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, details)
		return
	}

	tracing.SetSpanStatus(ctx, codes.Ok, "Posts listed successfully")
	tracing.AddGinSpanAttributes(c,
		attribute.Int("blog.total_count", resp.TotalCount),
		attribute.Int("blog.total_pages", resp.TotalPages),
	)

	response.Success(c, resp, nil)
}

package api

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/webnew-backend/wn-backend-v2/modules/blog/api/handlers"
	"github.com/webnew-backend/wn-backend-v2/modules/blog/configs"
	"github.com/webnew-backend/wn-backend-v2/modules/blog/repository"
	"github.com/webnew-backend/wn-backend-v2/modules/blog/service"
	"github.com/webnew-backend/wn-backend-v2/pkg/auth"
	"github.com/webnew-backend/wn-backend-v2/pkg/middleware"
)

// RegisterRoutes registers all API routes for the blog module
func RegisterRoutes(router *gin.Engine, categoryService service.CategoryService, postService service.PostService, tagService service.TagService, authorService service.AuthorService, menuService service.MenuService, jwtService *auth.JWTService, permService middleware.PermissionService) {
	// Set the config for tracing handlers if needed
	handlers.SetConfig(nil)

	// Create handlers
	categoryHandler := handlers.NewCategoryHandler(categoryService, jwtService)
	postHandler := handlers.NewPostHandler(postService, jwtService)
	tagHandler := handlers.NewTagHandler(tagService, jwtService)
	authorHandler := handlers.NewAuthorHandler(authorService, jwtService)
	menuHandler := handlers.NewMenuHandler(menuService, jwtService)

	// API Group
	apiGroup := router.Group("/api/v1/blog")

	// Apply JWT middleware to all routes
	apiGroup.Use(jwtService.JWTAuthMiddleware())

	// Categories API
	categoriesGroup := apiGroup.Group("/categories")
	{
		// Get all categories (with pagination)
		categoriesGroup.GET("", middleware.RequirePermission(permService, "blog.categories.read"), categoryHandler.List)

		// Get all categories as tree
		categoriesGroup.GET("/tree", middleware.RequirePermission(permService, "blog.categories.read"), categoryHandler.GetTree)

		// Create new category
		categoriesGroup.POST("", middleware.RequirePermission(permService, "blog.categories.create"), categoryHandler.Create)

		// Get category by ID
		categoriesGroup.GET("/:id", middleware.RequirePermission(permService, "blog.categories.read"), categoryHandler.Get)

		// Get category by slug
		categoriesGroup.GET("/slug/:slug", middleware.RequirePermission(permService, "blog.categories.read"), categoryHandler.GetBySlug)

		// Get category subtree
		categoriesGroup.GET("/:id/subtree", middleware.RequirePermission(permService, "blog.categories.read"), categoryHandler.GetSubtree)

		// Update category
		categoriesGroup.PUT("/:id", middleware.RequirePermission(permService, "blog.categories.update"), categoryHandler.Update)

		// Delete category
		categoriesGroup.DELETE("/:id", middleware.RequirePermission(permService, "blog.categories.delete"), categoryHandler.Delete)

		// Move category node
		categoriesGroup.POST("/move-node", middleware.RequirePermission(permService, "blog.categories.update"), categoryHandler.MoveNode)

		// Move category node to root level
		categoriesGroup.POST("/move-node-root", middleware.RequirePermission(permService, "blog.categories.update"), categoryHandler.MoveNodeRoot)

		// Update category position
		categoriesGroup.POST("/update-position", middleware.RequirePermission(permService, "blog.categories.update"), categoryHandler.UpdatePosition)

		// Rebuild category tree
		categoriesGroup.POST("/rebuild", middleware.RequirePermission(permService, "blog.categories.update"), categoryHandler.RebuildTree)

	}

	// Menu API
	menuGroup := apiGroup.Group("/menu")
	{
		// Get all menu items (with pagination)
		menuGroup.GET("", middleware.RequirePermission(permService, "blog.menu.read"), menuHandler.List)

		// Get all menu items as tree
		menuGroup.GET("/tree", middleware.RequirePermission(permService, "blog.menu.read"), menuHandler.GetTree)

		// Create new menu item
		menuGroup.POST("", middleware.RequirePermission(permService, "blog.menu.create"), menuHandler.Create)

		// Get menu item by ID
		menuGroup.GET("/:id", middleware.RequirePermission(permService, "blog.menu.read"), menuHandler.Get)

		// Get menu item by slug
		menuGroup.GET("/slug/:slug", middleware.RequirePermission(permService, "blog.menu.read"), menuHandler.GetBySlug)

		// Get menu subtree
		menuGroup.GET("/:id/subtree", middleware.RequirePermission(permService, "blog.menu.read"), menuHandler.GetSubtree)

		// Update menu item
		menuGroup.PUT("/:id", middleware.RequirePermission(permService, "blog.menu.update"), menuHandler.Update)

		// Delete menu item
		menuGroup.DELETE("/:id", middleware.RequirePermission(permService, "blog.menu.delete"), menuHandler.Delete)

		// Move menu item node
		menuGroup.POST("/move-node", middleware.RequirePermission(permService, "blog.menu.update"), menuHandler.MoveNode)

		// Move menu item node to root level
		menuGroup.POST("/move-node-root", middleware.RequirePermission(permService, "blog.menu.update"), menuHandler.MoveNodeRoot)

		// Update menu item position
		menuGroup.POST("/update-position", middleware.RequirePermission(permService, "blog.menu.update"), menuHandler.UpdatePosition)

		// Rebuild menu tree
		menuGroup.POST("/rebuild", middleware.RequirePermission(permService, "blog.menu.update"), menuHandler.RebuildTree)
	}

	// Posts API
	postsGroup := apiGroup.Group("/posts")
	{
		// Get all posts (with pagination)
		postsGroup.GET("", middleware.RequirePermission(permService, "blog.posts.read"), postHandler.List)

		// Create new post
		postsGroup.POST("", middleware.RequirePermission(permService, "blog.posts.create"), postHandler.Create)

		// Get post by ID
		postsGroup.GET("/:id", middleware.RequirePermission(permService, "blog.posts.read"), postHandler.Get)

		// Get post by slug
		postsGroup.GET("/slug/:slug", middleware.RequirePermission(permService, "blog.posts.read"), postHandler.GetBySlug)

		// Update post
		postsGroup.PUT("/:id", middleware.RequirePermission(permService, "blog.posts.update"), postHandler.Update)

		// Delete post
		postsGroup.DELETE("/:id", middleware.RequirePermission(permService, "blog.posts.delete"), postHandler.Delete)
	}

	// Tags API
	tagsGroup := apiGroup.Group("/tags")
	{
		// Get all tags (with pagination)
		tagsGroup.GET("", middleware.RequirePermission(permService, "blog.tags.read"), tagHandler.List)

		// Get tags with post count
		tagsGroup.GET("/with-post-count", middleware.RequirePermission(permService, "blog.tags.read"), tagHandler.GetWithPostCount)

		// Create new tag
		tagsGroup.POST("", middleware.RequirePermission(permService, "blog.tags.create"), tagHandler.Create)

		// Get tag by ID
		tagsGroup.GET("/:id", middleware.RequirePermission(permService, "blog.tags.read"), tagHandler.Get)

		// Get tag by slug
		tagsGroup.GET("/slug/:slug", middleware.RequirePermission(permService, "blog.tags.read"), tagHandler.GetBySlug)

		// Update tag
		tagsGroup.PUT("/:id", middleware.RequirePermission(permService, "blog.tags.update"), tagHandler.Update)

		// Delete tag
		tagsGroup.DELETE("/:id", middleware.RequirePermission(permService, "blog.tags.delete"), tagHandler.Delete)
	}

	// Authors API
	authorsGroup := apiGroup.Group("/authors")
	{
		// Get all authors (with pagination)
		authorsGroup.GET("", middleware.RequirePermission(permService, "blog.authors.read"), authorHandler.List)

		// Create new author
		authorsGroup.POST("", middleware.RequirePermission(permService, "blog.authors.create"), authorHandler.Create)

		// Get author by ID
		authorsGroup.GET("/:id", middleware.RequirePermission(permService, "blog.authors.read"), authorHandler.Get)

		// Get author by user ID
		authorsGroup.GET("/user/:user_id", middleware.RequirePermission(permService, "blog.authors.read"), authorHandler.GetByUserId)

		// Update author
		authorsGroup.PUT("/:id", middleware.RequirePermission(permService, "blog.authors.update"), authorHandler.Update)

		// Delete author
		authorsGroup.DELETE("/:id", middleware.RequirePermission(permService, "blog.authors.delete"), authorHandler.Delete)
	}
}

// SetupRoutes sets up all routes for the blog module
func SetupRoutes(router *gin.Engine, db *gorm.DB, jwtSvc *auth.JWTService, cfg *configs.Config, permService middleware.PermissionService) {
	// Initialize repositories
	postRepo := repository.NewPostRepository(db)
	categoryRepo := repository.NewCategoryRepository(db)
	tagRepo := repository.NewTagRepository(db)
	authorRepo := repository.NewAuthorRepository(db)
	menuRepo := repository.NewMenuRepository(db)

	// Initialize services
	postService := service.NewPostService(postRepo, categoryRepo, tagRepo, authorRepo)
	categoryService := service.NewCategoryService(categoryRepo)
	tagService := service.NewTagService(tagRepo)
	authorService := service.NewAuthorService(authorRepo)
	menuService := service.NewMenuService(menuRepo)

	// Register routes
	RegisterRoutes(router, categoryService, postService, tagService, authorService, menuService, jwtSvc, permService)
}

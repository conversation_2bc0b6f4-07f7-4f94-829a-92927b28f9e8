package api

import (
	"fmt"
	"wnapi/internal/core"
	"wnapi/internal/middleware"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/blog/api/handlers"
	"wnapi/modules/blog/internal"

	"github.com/gin-gonic/gin"
)

// <PERSON>ler is the main API handler for the blog module
type Handler struct {
	postHandler     *handlers.PostHandler
	categoryHandler *handlers.CategoryHandler
	commentHandler  *handlers.CommentHandler
	routes          []string
}

// NewHandler creates a new handler
func <PERSON>Handler(blogService internal.BlogService) *Handler {
	return &Handler{
		postHandler:     handlers.NewPostHandler(blogService),
		categoryHandler: handlers.NewCategoryHandler(blogService),
		commentHandler:  handlers.NewCommentHandler(blogService),
		routes:          make([]string, 0),
	}
}

// RegisterRoutes registers all routes for the blog module
func (h *Handler) RegisterRoutes(server *core.Server) error {
	// API Group
	apiGroup := server.Group("/api/v1/blog")

	// Add tracing middleware for all blog routes
	apiGroup.Use(tracing.GinMiddleware("blog"))

	// Save the list of routes for display
	basePath := "/api/v1/blog"

	// Health check endpoint
	apiGroup.GET("/healthy", h.healthCheck)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/healthy", basePath))

	// Public routes - no authentication required
	apiGroup.GET("/posts", h.postHandler.ListPosts)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/posts", basePath))

	apiGroup.GET("/posts/:id", h.postHandler.GetPost)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/posts/:id", basePath))

	apiGroup.GET("/posts/slug/:slug", h.postHandler.GetPostBySlug)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/posts/slug/:slug", basePath))

	apiGroup.GET("/categories", h.categoryHandler.ListCategories)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/categories", basePath))

	apiGroup.GET("/categories/:id", h.categoryHandler.GetCategory)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/categories/:id", basePath))

	apiGroup.GET("/posts/:postId/comments", h.commentHandler.GetCommentsByPost)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/posts/:postId/comments", basePath))

	// Protected routes - authentication required
	authorized := apiGroup.Group("")
	authorized.Use(middleware.JWTAuth())

	// Post management
	authorized.POST("/posts", h.postHandler.CreatePost)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/posts", basePath))

	authorized.PUT("/posts/:id", h.postHandler.UpdatePost)
	h.routes = append(h.routes, fmt.Sprintf("PUT %s/posts/:id", basePath))

	authorized.DELETE("/posts/:id", h.postHandler.DeletePost)
	h.routes = append(h.routes, fmt.Sprintf("DELETE %s/posts/:id", basePath))

	// Category management
	authorized.POST("/categories", h.categoryHandler.CreateCategory)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/categories", basePath))

	authorized.PUT("/categories/:id", h.categoryHandler.UpdateCategory)
	h.routes = append(h.routes, fmt.Sprintf("PUT %s/categories/:id", basePath))

	authorized.DELETE("/categories/:id", h.categoryHandler.DeleteCategory)
	h.routes = append(h.routes, fmt.Sprintf("DELETE %s/categories/:id", basePath))

	// Comment management
	authorized.POST("/comments", h.commentHandler.CreateComment)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/comments", basePath))

	authorized.PUT("/comments/:id", h.commentHandler.UpdateComment)
	h.routes = append(h.routes, fmt.Sprintf("PUT %s/comments/:id", basePath))

	authorized.DELETE("/comments/:id", h.commentHandler.DeleteComment)
	h.routes = append(h.routes, fmt.Sprintf("DELETE %s/comments/:id", basePath))

	// Admin routes - admin role required
	admin := apiGroup.Group("/admin")
	admin.Use(middleware.JWTAuth(), middleware.RoleRequired("admin"))

	// Admin-specific operations
	// ...

	return nil
}

// healthCheck is an endpoint to check the status of the module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"module":  "blog",
		"message": "Blog module is running",
	})
}

package repository

import (
	"context"
	"errors"
	"fmt"

	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/blog/dto"
	"wnapi/modules/blog/internal"

	"go.opentelemetry.io/otel/attribute"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// mysqlRepository implements Repository interface using GORM
type mysqlRepository struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewMySQLRepository creates a new repository
func NewMySQLRepository(dbManager *database.Manager, logger logger.Logger) (internal.Repository, error) {
	if dbManager == nil {
		return nil, errors.New("database manager is nil")
	}

	sqlxDB := dbManager.GetDB()
	if sqlxDB == nil {
		return nil, errors.New("cannot connect to database")
	}

	// Create GORM DB from sqlx DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlxDB.DB,
	}), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to initialize GORM: %w", err)
	}

	return &mysqlRepository{
		db:     gormDB,
		logger: logger,
	}, nil
}

// CreatePost creates a new blog post
func (r *mysqlRepository) CreatePost(ctx context.Context, post *internal.Post) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "blog_posts")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "blog_posts"),
		attribute.String("db.operation", "create_post"),
	)

	result := r.db.WithContext(ctx).Create(post)
	if result.Error != nil {
		r.logger.Error("Failed to create post", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return result.Error
	}

	return nil
}

// GetPostByID retrieves a post by its ID
func (r *mysqlRepository) GetPostByID(ctx context.Context, id int) (*internal.Post, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "blog_posts")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "blog_posts"),
		attribute.String("db.operation", "get_post_by_id"),
		attribute.Int("blog.post_id", id),
	)

	var post internal.Post
	result := r.db.WithContext(ctx).First(&post, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", false))
			return nil, internal.ErrPostNotFound
		}
		r.logger.Error("Failed to get post", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, result.Error
	}

	tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", true))
	return &post, nil
}

// GetPostBySlug retrieves a post by its slug
func (r *mysqlRepository) GetPostBySlug(ctx context.Context, slug string) (*internal.Post, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "blog_posts")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "blog_posts"),
		attribute.String("db.operation", "get_post_by_slug"),
		attribute.String("blog.slug", slug),
	)

	var post internal.Post
	result := r.db.WithContext(ctx).Where("slug = ?", slug).First(&post)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", false))
			return nil, internal.ErrPostNotFound
		}
		r.logger.Error("Failed to get post by slug", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, result.Error
	}

	tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", true))
	return &post, nil
}

// UpdatePost updates an existing post
func (r *mysqlRepository) UpdatePost(ctx context.Context, post *internal.Post) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "blog_posts")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "blog_posts"),
		attribute.String("db.operation", "update_post"),
		attribute.Int("blog.post_id", post.ID),
	)

	result := r.db.WithContext(ctx).Save(post)
	if result.Error != nil {
		r.logger.Error("Failed to update post", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return result.Error
	}

	if result.RowsAffected == 0 {
		return internal.ErrPostNotFound
	}

	return nil
}

// DeletePost deletes a post by its ID
func (r *mysqlRepository) DeletePost(ctx context.Context, id int) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "blog_posts")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "blog_posts"),
		attribute.String("db.operation", "delete_post"),
		attribute.Int("blog.post_id", id),
	)

	result := r.db.WithContext(ctx).Delete(&internal.Post{}, id)
	if result.Error != nil {
		r.logger.Error("Failed to delete post", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return result.Error
	}

	if result.RowsAffected == 0 {
		return internal.ErrPostNotFound
	}

	return nil
}

// ListPosts lists posts with filtering
func (r *mysqlRepository) ListPosts(ctx context.Context, filter dto.PostFilter) ([]*internal.Post, int, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "blog_posts")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "blog_posts"),
		attribute.String("db.operation", "list_posts"),
		attribute.Int("blog.page", filter.Page),
		attribute.Int("blog.per_page", filter.PerPage),
	)

	// Set default values
	if filter.Page <= 0 {
		filter.Page = 1
	}
	if filter.PerPage <= 0 {
		filter.PerPage = 10
	}

	// Start building the query
	query := r.db.WithContext(ctx).Model(&internal.Post{})

	// Apply filters
	if filter.CategoryID > 0 {
		query = query.Where("category_id = ?", filter.CategoryID)
	}
	if filter.AuthorID > 0 {
		query = query.Where("author_id = ?", filter.AuthorID)
	}
	if filter.Status != "" && filter.Status != "all" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.Search != "" {
		searchTerm := "%" + filter.Search + "%"
		query = query.Where("title LIKE ? OR content LIKE ?", searchTerm, searchTerm)
	}

	// Count total records for pagination
	var totalCount int64
	if err := query.Count(&totalCount).Error; err != nil {
		r.logger.Error("Failed to count posts", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, err
	}

	// Apply pagination
	offset := (filter.Page - 1) * filter.PerPage
	query = query.Offset(offset).Limit(filter.PerPage)

	// Apply ordering
	query = query.Order("created_at DESC")

	// Execute the query
	var posts []*internal.Post
	if err := query.Find(&posts).Error; err != nil {
		r.logger.Error("Failed to list posts", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, err
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Int64("blog.total_count", totalCount),
		attribute.Int("blog.returned_count", len(posts)),
	)

	return posts, int(totalCount), nil
}

// Implement other repository methods (Category and Comment CRUD operations) following the same pattern
// ...

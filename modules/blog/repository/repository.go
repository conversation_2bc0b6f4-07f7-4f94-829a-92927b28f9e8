package repository

import (
	"context"

	"github.com/webnew/wn-backend-v2/modules/blog/dto/request"
	"github.com/webnew/wn-backend-v2/modules/blog/models"
)

// AuthorRepository defines the interface for author data operations
type AuthorRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, author *models.BlogAuthor) error
	GetByID(ctx context.Context, tenantID, authorID uint) (*models.BlogAuthor, error)
	GetByUserID(ctx context.Context, tenantID, userID uint) (*models.BlogAuthor, error)
	Update(ctx context.Context, author *models.BlogAuthor) error
	Delete(ctx context.Context, tenantID, authorID uint) error

	// List with cursor pagination
	List(ctx context.Context, tenantID uint, req request.ListAuthorRequest) ([]*models.BlogAuthor, string, bool, error)
}

// CategoryRepository defines the interface for category data operations
type CategoryRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, category *models.Category) error
	GetByID(ctx context.Context, tenantID, categoryID uint) (*models.Category, error)
	GetBySlug(ctx context.Context, tenantID uint, slug string) (*models.Category, error)
	Update(ctx context.Context, category *models.Category) error
	Delete(ctx context.Context, tenantID, categoryID uint) error

	// Tree operations
	GetTree(ctx context.Context, tenantID uint) ([]*models.Category, error)
	GetSubtree(ctx context.Context, tenantID, categoryID uint) ([]*models.Category, error)
	GetAncestors(ctx context.Context, tenantID, categoryID uint) ([]*models.Category, error)
	GetChildren(ctx context.Context, tenantID, categoryID uint) ([]*models.Category, error)
	RebuildTree(ctx context.Context, tenantID uint) error

	// Movement operations
	MoveNode(ctx context.Context, tenantID, categoryID, targetID uint, position string) error
	ChangeParent(ctx context.Context, tenantID, categoryID, newParentID uint) error

	// Procedure operations
	MoveNodeProcedure(ctx context.Context, tenantID, categoryID, newParentID uint, position int) error
	UpdatePositionProcedure(ctx context.Context, tenantID, categoryID uint, position int) error
	MoveNodeSiblingProcedure(ctx context.Context, tenantID, categoryID, targetID uint) error
	MoveNodeRelativeProcedure(ctx context.Context, tenantID, categoryID, targetID uint, mode string) error

	// List with cursor pagination
	List(ctx context.Context, tenantID uint, req request.ListCategoryRequest) ([]*models.Category, string, bool, error)

	// Statistics
	GetCategoriesWithPostCount(ctx context.Context, tenantID uint) ([]*models.Category, error)
}

// PostRepository defines the interface for post data operations
type PostRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, post *models.Post) error
	GetByID(ctx context.Context, tenantID, postID uint) (*models.Post, error)
	GetBySlug(ctx context.Context, tenantID uint, slug string) (*models.Post, error)
	Update(ctx context.Context, post *models.Post) error
	Delete(ctx context.Context, tenantID, postID uint) error

	// List with cursor pagination
	List(ctx context.Context, tenantID uint, req request.ListPostRequest) ([]*models.Post, string, bool, error)

	// Category and tag relations
	GetPostCategories(ctx context.Context, tenantID, postID uint) ([]uint, error)
	SetPostCategories(ctx context.Context, tenantID, postID uint, categoryIDs []uint) error
	GetPostTags(ctx context.Context, tenantID, postID uint) ([]uint, error)
	SetPostTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error

	// Statistics
	CountPosts(ctx context.Context, tenantID uint, status *string, categoryID *uint) (int, error)
}

// TagRepository defines the interface for tag data operations
type TagRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, tag *models.Tag) error
	GetByID(ctx context.Context, tenantID, tagID uint) (*models.Tag, error)
	GetBySlug(ctx context.Context, tenantID uint, slug string) (*models.Tag, error)
	Update(ctx context.Context, tag *models.Tag) error
	Delete(ctx context.Context, tenantID, tagID uint) error

	// List with cursor pagination
	List(ctx context.Context, tenantID uint, req request.ListTagRequest) ([]*models.Tag, string, bool, error)

	// Statistics
	GetTagsWithPostCount(ctx context.Context, tenantID uint) ([]*models.Tag, error)
}

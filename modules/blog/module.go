package blog

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"

	"github.com/webnew/wn-backend-v2/modules/blog/api"
	"github.com/webnew/wn-backend-v2/modules/blog/configs"
	"github.com/webnew/wn-backend-v2/modules/blog/repository/mysql"
	"github.com/webnew/wn-backend-v2/modules/blog/service"
	"github.com/webnew/wn-backend-v2/modules/blog/tracing"
	"github.com/webnew/wn-backend-v2/pkg/auth"
)

// Module represents the blog module
type Module struct {
	DB              *sqlx.DB
	Config          *configs.Config
	server          *http.Server
	router          *gin.Engine
	categoryService service.CategoryService
	postService     service.PostService
	tagService      service.TagService
	authorService   service.AuthorService
}

// NewModule creates a new instance of the blog module
func NewModule(db *sqlx.DB) *Module {
	return &Module{
		DB: db,
	}
}

// NewModuleWithConfig creates a new instance of the blog module with configuration
func NewModuleWithConfig(cfg *configs.Config) *Module {
	// Set up database connection
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true",
		cfg.DB.Username,
		cfg.DB.Password,
		cfg.DB.Host,
		cfg.DB.Port,
		cfg.DB.Database,
	)

	db, err := sqlx.Connect("mysql", dsn)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Set connection pool settings
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	// Khởi tạo tracing
	if err := tracing.InitTracing(cfg); err != nil {
		log.Printf("WARN: Không thể khởi tạo tracing: %v", err)
	}

	return &Module{
		DB:     db,
		Config: cfg,
		router: gin.Default(),
	}
}

// RegisterRoutes registers all routes for the blog module
func (m *Module) RegisterRoutes(router *gin.Engine) {
	// Create repositories
	categoryRepo := mysql.NewCategoryRepository(m.DB)
	postRepo := mysql.NewPostRepository(m.DB)
	tagRepo := mysql.NewTagRepository(m.DB)
	authorRepo := mysql.NewAuthorRepository(m.DB)

	// Create services
	categoryService := service.NewCategoryService(categoryRepo)
	postService := service.NewPostService(postRepo, categoryRepo, tagRepo)
	tagService := service.NewTagService(tagRepo)
	authorService := service.NewAuthorService(authorRepo)

	// Save services in module for potential external use
	m.categoryService = categoryService
	m.postService = postService
	m.tagService = tagService
	m.authorService = authorService

	// Create JWT service
	accessTokenExpiration, err := time.ParseDuration(m.Config.JWT.AccessTokenExpiration)
	if err != nil {
		log.Printf("Error parsing access token expiration: %v, using default 24h", err)
		accessTokenExpiration = 24 * time.Hour
	}

	refreshTokenExpiration, err := time.ParseDuration(m.Config.JWT.RefreshTokenExpiration)
	if err != nil {
		log.Printf("Error parsing refresh token expiration: %v, using default 720h", err)
		refreshTokenExpiration = 720 * time.Hour
	}

	jwtConfig := auth.JWTConfig{
		AccessSigningKey:       m.Config.JWT.AccessSigningKey,
		RefreshSigningKey:      m.Config.JWT.RefreshSigningKey,
		AccessTokenExpiration:  accessTokenExpiration,
		RefreshTokenExpiration: refreshTokenExpiration,
		Issuer:                 m.Config.JWT.Issuer,
	}
	jwtService := auth.NewJWTService(jwtConfig)

	// Register routes
	api.RegisterRoutes(router, categoryService, postService, tagService, authorService, jwtService)
}

// Start starts the blog module server
func (m *Module) Start() error {
	// If router is not set, use the one from the module
	if m.router == nil {
		m.router = gin.Default()
	}

	// Register routes
	m.RegisterRoutes(m.router)

	// Create HTTP server
	m.server = &http.Server{
		Addr:    fmt.Sprintf("%s:%d", m.Config.Server.Host, m.Config.Server.Port),
		Handler: m.router,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting blog service on %s:%d", m.Config.Server.Host, m.Config.Server.Port)
		if err := m.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	return nil
}

// Stop gracefully stops the blog module server
func (m *Module) Stop() error {
	if m.server == nil {
		return nil
	}

	// Create a context with timeout for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Shutdown server
	if err := m.server.Shutdown(ctx); err != nil {
		return fmt.Errorf("server shutdown failed: %w", err)
	}

	// Close database connection
	if m.DB != nil {
		if err := m.DB.Close(); err != nil {
			return fmt.Errorf("database connection close failed: %w", err)
		}
	}

	// Đóng tracer
	tracing.CloseTracer()

	log.Println("Blog service stopped gracefully")
	return nil
}

package blog

import (
	"path/filepath"

	"wnapi/internal/core"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/blog/api"
	"wnapi/modules/blog/internal"
	"wnapi/modules/blog/repository"
	"wnapi/modules/blog/service"
)

func init() {
	core.RegisterModuleFactory("blog", NewModule)
}

// Module implements the blog module
type Module struct {
	name    string
	logger  logger.Logger
	config  map[string]interface{}
	app     *core.App
	handler *api.Handler
}

// NewModule creates a new blog module
func NewModule(app *core.App, config map[string]interface{}) (core.Module, error) {
	logger := app.GetLogger()

	// Initialize repository
	repo, err := repository.NewMySQLRepository(app.GetDBManager(), logger)
	if err != nil {
		return nil, err
	}

	// Load configuration from environment variables
	blogConfig, err := internal.LoadBlogConfig()
	if err != nil {
		logger.Warn("Cannot load config from environment variables, using defaults: %v", err)
		// Initialize default config if needed
	}

	// Initialize service and handler
	blogService := service.NewService(repo, *blogConfig, logger)
	handler := api.NewHandler(blogService)

	return &Module{
		name:    "blog",
		logger:  logger,
		config:  config,
		app:     app,
		handler: handler,
	}, nil
}

// Name returns the module name
func (m *Module) Name() string {
	return m.name
}

// Init initializes the module
func (m *Module) Init() error {
	m.logger.Info("Initializing blog module")
	return nil
}

// RegisterRoutes registers the module routes
func (m *Module) RegisterRoutes(server *core.Server) error {
	return registerRoutes(server, m.handler)
}

// Cleanup performs cleanup before module shutdown
func (m *Module) Cleanup() error {
	m.logger.Info("Cleaning up blog module")
	return nil
}

// GetMigrationPath returns the path to the migration files
func (m *Module) GetMigrationPath() string {
	return filepath.Join("modules", "blog", "migrations")
}

// GetMigrationOrder returns the order in which migrations should be executed
func (m *Module) GetMigrationOrder() int {
	return 10 // Execute after core modules like auth (usually 1-5)
}

package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	_ "github.com/go-sql-driver/mysql"

	"github.com/webnew/wn-backend-v2/modules/blog"
	"github.com/webnew/wn-backend-v2/modules/blog/configs"
)

func main() {
	// Load config
	config, err := configs.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize module
	module := blog.NewModuleWithConfig(config)

	// Start the module
	if err := module.Start(); err != nil {
		log.Fatalf("Failed to start module: %v", err)
	}

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	// Kill (no param) default sends syscall.SIGTERM
	// Kill -2 is syscall.SIGINT
	// Kill -9 is syscall.SIGKILL but can't be caught, so don't need to add it
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down blog module...")

	// Stop the module
	if err := module.Stop(); err != nil {
		log.Fatalf("Failed to stop module: %v", err)
	}

	log.Println("Blog module shutdown complete")
}

const express = require('express');
const router = express.Router();
const NotificationController = require('./notification.controller');
const { authMiddleware } = require('../../middleware/auth');

// Apply auth middleware to all notification routes
router.use(authMiddleware);

// Get all notifications with pagination and filters
router.get('/', NotificationController.getAllNotifications);

// Get notification by ID
router.get('/:id', NotificationController.getNotificationById);

// Mark notification as read
router.patch('/:id/read', NotificationController.markAsRead);

// Mark all notifications as read
router.patch('/mark-all-read', NotificationController.markAllAsRead);

// Delete notification
router.delete('/:id', NotificationController.deleteNotification);

// Get unread notifications count
router.get('/unread-count', NotificationController.getUnreadCount);

// Update notification settings
router.put('/settings', NotificationController.updateNotificationSettings);

module.exports = router;

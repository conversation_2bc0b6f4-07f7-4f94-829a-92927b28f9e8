const Notification = require('./notification.model');
const NotificationSettings = require('./notification.settings.model');

class NotificationService {
  async getAllNotifications(userId, page, limit, status) {
    const skip = (page - 1) * limit;
    
    const query = { userId };
    if (status === 'read') query.isRead = true;
    if (status === 'unread') query.isRead = false;
    
    const notifications = await Notification.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));
      
    const total = await Notification.countDocuments(query);
    
    return {
      notifications,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit)
      }
    };
  }

  async getNotificationById(id, userId) {
    return await Notification.findOne({ _id: id, userId });
  }

  async markAsRead(id, userId) {
    return await Notification.findOneAndUpdate(
      { _id: id, userId },
      { isRead: true },
      { new: true }
    );
  }

  async markAllAsRead(userId) {
    return await Notification.updateMany(
      { userId, isRead: false },
      { isRead: true }
    );
  }

  async deleteNotification(id, userId) {
    return await Notification.findOneAndDelete({ _id: id, userId });
  }

  async getUnreadCount(userId) {
    return await Notification.countDocuments({ userId, isRead: false });
  }

  async updateSettings(userId, settings) {
    return await NotificationSettings.findOneAndUpdate(
      { userId },
      settings,
      { new: true, upsert: true }
    );
  }

  async createNotification(data) {
    const { userId, type, content, referenceId, referenceType } = data;
    
    // Check user notification settings before creating
    const settings = await NotificationSettings.findOne({ userId });
    
    if (settings && type in settings.notificationTypes) {
      if (!settings.notificationTypes[type]) {
        return null; // User has disabled this notification type
      }
    }
    
    return await Notification.create({
      userId,
      type,
      content,
      referenceId,
      referenceType,
      isRead: false,
      createdAt: new Date()
    });
  }
}

module.exports = new NotificationService();

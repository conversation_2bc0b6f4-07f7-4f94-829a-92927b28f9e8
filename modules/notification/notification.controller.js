const NotificationService = require('./notification.service');

class NotificationController {
  async getAllNotifications(req, res) {
    try {
      const { page = 1, limit = 20, status } = req.query;
      const userId = req.user.id;
      const notifications = await NotificationService.getAllNotifications(userId, page, limit, status);
      return res.status(200).json(notifications);
    } catch (error) {
      return res.status(500).json({ message: 'Failed to fetch notifications', error: error.message });
    }
  }

  async getNotificationById(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      const notification = await NotificationService.getNotificationById(id, userId);
      
      if (!notification) {
        return res.status(404).json({ message: 'Notification not found' });
      }
      
      return res.status(200).json(notification);
    } catch (error) {
      return res.status(500).json({ message: 'Failed to fetch notification', error: error.message });
    }
  }

  async markAsRead(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      const notification = await NotificationService.markAsRead(id, userId);
      
      if (!notification) {
        return res.status(404).json({ message: 'Notification not found' });
      }
      
      return res.status(200).json({ message: 'Notification marked as read', notification });
    } catch (error) {
      return res.status(500).json({ message: 'Failed to mark notification as read', error: error.message });
    }
  }

  async markAllAsRead(req, res) {
    try {
      const userId = req.user.id;
      await NotificationService.markAllAsRead(userId);
      return res.status(200).json({ message: 'All notifications marked as read' });
    } catch (error) {
      return res.status(500).json({ message: 'Failed to mark all notifications as read', error: error.message });
    }
  }

  async deleteNotification(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      const result = await NotificationService.deleteNotification(id, userId);
      
      if (!result) {
        return res.status(404).json({ message: 'Notification not found' });
      }
      
      return res.status(200).json({ message: 'Notification deleted successfully' });
    } catch (error) {
      return res.status(500).json({ message: 'Failed to delete notification', error: error.message });
    }
  }

  async getUnreadCount(req, res) {
    try {
      const userId = req.user.id;
      const count = await NotificationService.getUnreadCount(userId);
      return res.status(200).json({ count });
    } catch (error) {
      return res.status(500).json({ message: 'Failed to get unread count', error: error.message });
    }
  }

  async updateNotificationSettings(req, res) {
    try {
      const userId = req.user.id;
      const settings = req.body;
      const updatedSettings = await NotificationService.updateSettings(userId, settings);
      return res.status(200).json({ message: 'Notification settings updated', settings: updatedSettings });
    } catch (error) {
      return res.status(500).json({ message: 'Failed to update notification settings', error: error.message });
    }
  }
}

module.exports = new NotificationController();

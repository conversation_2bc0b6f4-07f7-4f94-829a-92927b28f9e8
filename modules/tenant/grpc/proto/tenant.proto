syntax = "proto3";

package tenant;

option go_package = "github.com/webnew/wn-backend-v2/modules/tenant/grpc/proto;tenantpb";

service TenantService {
  // Get tenant detail by ID
  rpc GetTenant(GetTenantRequest) returns (Tenant) {}
  
  // Create a new tenant
  rpc CreateTenant(CreateTenantRequest) returns (Tenant) {}
  
  // Update existing tenant
  rpc UpdateTenant(UpdateTenantRequest) returns (Tenant) {}
  
  // Delete a tenant
  rpc DeleteTenant(DeleteTenantRequest) returns (DeleteTenantResponse) {}
  
  // List all tenants with pagination
  rpc ListTenants(ListTenantsRequest) returns (ListTenantsResponse) {}
  
  // Update tenant status
  rpc UpdateTenantStatus(UpdateTenantStatusRequest) returns (Tenant) {}
  
  // Get tenant by code
  rpc GetTenantByCode(GetTenantByCodeRequest) returns (Tenant) {}
}

message GetTenantRequest {
  int64 id = 1;
}

message CreateTenantRequest {
  string name = 1;
  string email = 2;
  string code = 3;
  string phone = 4;
  string address = 5;
  string website = 6;
  string description = 7;
}

message UpdateTenantRequest {
  int64 id = 1;
  string name = 2;
  string email = 3;
  string phone = 4;
  string address = 5;
  string website = 6;
  string description = 7;
}

message DeleteTenantRequest {
  int64 id = 1;
}

message DeleteTenantResponse {
  bool success = 1;
  string message = 2;
}

message ListTenantsRequest {
  int32 page_size = 1;
  string next_cursor = 2;
  string search = 3;
  string status = 4;
}

message ListTenantsResponse {
  repeated Tenant tenants = 1;
  string next_cursor = 2;
  bool has_more = 3;
}

message UpdateTenantStatusRequest {
  int64 id = 1;
  string status = 2; // active, inactive, suspended
}

message GetTenantByCodeRequest {
  string code = 1;
}

message Tenant {
  int64 id = 1;
  string name = 2;
  string email = 3;
  string code = 4;
  string phone = 5;
  string address = 6;
  string website = 7;
  string description = 8;
  string status = 9;
  string created_at = 10;
  string updated_at = 11;
}

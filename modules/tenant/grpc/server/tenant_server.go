package server

import (
	"context"
	"fmt"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	tenantpb "github.com/webnew/wn-backend-v2/modules/tenant/grpc/proto"
	"github.com/webnew/wn-backend-v2/modules/tenant/repository"
	"github.com/webnew/wn-backend-v2/modules/tenant/tracing"
)

type TenantServer struct {
	tenantpb.UnimplementedTenantServiceServer
	repo repository.TenantRepository
}

func NewTenantServer() *TenantServer {
	return &TenantServer{}
}

// SetRepository thiết lập repository cho tenant server
func (s *TenantServer) SetRepository(repo repository.TenantRepository) {
	s.repo = repo
}

func (s *TenantServer) GetTenant(ctx context.Context, req *tenantpb.GetTenantRequest) (*tenantpb.Tenant, error) {
	// Bắt đầu span mới
	ctx, otSpan, jaegerSpan := tracing.StartTenantSpan(ctx, "grpc.tenant.get")
	defer tracing.FinishTenantSpan(otSpan, jaegerSpan)

	// Thêm thuộc tính tenant
	tenantAttrs := map[string]interface{}{
		"tenant.id":        req.Id,
		"tenant.resource":  "tenant",
		"tenant.operation": "get",
		"rpc.service":      "TenantService",
		"rpc.method":       "GetTenant",
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, tenantAttrs)

	if s.repo == nil {
		err := status.Error(codes.Internal, "repository not initialized")
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, err
	}

	tenant, err := s.repo.GetByID(ctx, uint(req.Id))
	if err != nil {
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, status.Errorf(codes.NotFound, "không tìm thấy tenant với ID %d: %v", req.Id, err)
	}

	// Thêm thông tin kết quả vào span
	responseAttrs := map[string]interface{}{
		"tenant.name":   tenant.Name,
		"tenant.status": tenant.Status,
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, responseAttrs)

	return convertToProtoTenant(tenant), nil
}

func (s *TenantServer) CreateTenant(ctx context.Context, req *tenantpb.CreateTenantRequest) (*tenantpb.Tenant, error) {
	// Bắt đầu span mới
	ctx, otSpan, jaegerSpan := tracing.StartTenantSpan(ctx, "grpc.tenant.create")
	defer tracing.FinishTenantSpan(otSpan, jaegerSpan)

	// Thêm thuộc tính tenant
	tenantAttrs := map[string]interface{}{
		"tenant.name":      req.Name,
		"tenant.resource":  "tenant",
		"tenant.operation": "create",
		"rpc.service":      "TenantService",
		"rpc.method":       "CreateTenant",
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, tenantAttrs)

	if s.repo == nil {
		err := status.Error(codes.Internal, "repository not initialized")
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, err
	}

	tenantData := repository.Tenant{
		Name:        req.Name,
		Email:       req.Email,
		Code:        req.Code,
		Phone:       req.Phone,
		Address:     req.Address,
		Website:     req.Website,
		Description: req.Description,
		Status:      "active", // Mặc định là active
	}

	tenant, err := s.repo.Create(ctx, tenantData)
	if err != nil {
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, status.Errorf(codes.Internal, "không thể tạo tenant: %v", err)
	}

	// Thêm thông tin kết quả vào span
	responseAttrs := map[string]interface{}{
		"tenant.id":     tenant.ID,
		"tenant.status": tenant.Status,
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, responseAttrs)

	return convertToProtoTenant(tenant), nil
}

func (s *TenantServer) UpdateTenant(ctx context.Context, req *tenantpb.UpdateTenantRequest) (*tenantpb.Tenant, error) {
	// Bắt đầu span mới
	ctx, otSpan, jaegerSpan := tracing.StartTenantSpan(ctx, "grpc.tenant.update")
	defer tracing.FinishTenantSpan(otSpan, jaegerSpan)

	// Thêm thuộc tính tenant
	tenantAttrs := map[string]interface{}{
		"tenant.id":        req.Id,
		"tenant.name":      req.Name,
		"tenant.resource":  "tenant",
		"tenant.operation": "update",
		"rpc.service":      "TenantService",
		"rpc.method":       "UpdateTenant",
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, tenantAttrs)

	if s.repo == nil {
		err := status.Error(codes.Internal, "repository not initialized")
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, err
	}

	tenantData := repository.Tenant{
		ID:          uint(req.Id),
		Name:        req.Name,
		Email:       req.Email,
		Phone:       req.Phone,
		Address:     req.Address,
		Website:     req.Website,
		Description: req.Description,
	}

	tenant, err := s.repo.Update(ctx, tenantData)
	if err != nil {
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, status.Errorf(codes.Internal, "không thể cập nhật tenant: %v", err)
	}

	return convertToProtoTenant(tenant), nil
}

func (s *TenantServer) DeleteTenant(ctx context.Context, req *tenantpb.DeleteTenantRequest) (*tenantpb.DeleteTenantResponse, error) {
	// Bắt đầu span mới
	ctx, otSpan, jaegerSpan := tracing.StartTenantSpan(ctx, "grpc.tenant.delete")
	defer tracing.FinishTenantSpan(otSpan, jaegerSpan)

	// Thêm thuộc tính tenant
	tenantAttrs := map[string]interface{}{
		"tenant.id":        req.Id,
		"tenant.resource":  "tenant",
		"tenant.operation": "delete",
		"rpc.service":      "TenantService",
		"rpc.method":       "DeleteTenant",
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, tenantAttrs)

	if s.repo == nil {
		err := status.Error(codes.Internal, "repository not initialized")
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, err
	}

	err := s.repo.Delete(ctx, uint(req.Id))
	if err != nil {
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, status.Errorf(codes.Internal, "không thể xóa tenant: %v", err)
	}

	return &tenantpb.DeleteTenantResponse{
		Success: true,
		Message: fmt.Sprintf("Đã xóa tenant với ID %d", req.Id),
	}, nil
}

func (s *TenantServer) ListTenants(ctx context.Context, req *tenantpb.ListTenantsRequest) (*tenantpb.ListTenantsResponse, error) {
	// Bắt đầu span mới
	ctx, otSpan, jaegerSpan := tracing.StartTenantSpan(ctx, "grpc.tenant.list")
	defer tracing.FinishTenantSpan(otSpan, jaegerSpan)

	// Thêm thuộc tính tenant
	tenantAttrs := map[string]interface{}{
		"tenant.resource":  "tenant",
		"tenant.operation": "list",
		"tenant.status":    req.Status,
		"tenant.search":    req.Search,
		"tenant.page_size": req.PageSize,
		"tenant.cursor":    req.NextCursor,
		"rpc.service":      "TenantService",
		"rpc.method":       "ListTenants",
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, tenantAttrs)

	if s.repo == nil {
		err := status.Error(codes.Internal, "repository not initialized")
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, err
	}

	pageSize := 10
	if req.PageSize > 0 {
		pageSize = int(req.PageSize)
	}

	params := repository.ListParams{
		Cursor: req.NextCursor,
		Limit:  pageSize,
		Search: req.Search,
		Status: req.Status,
	}

	tenants, nextCursor, hasMore, err := s.repo.List(ctx, params)
	if err != nil {
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, status.Errorf(codes.Internal, "không thể lấy danh sách tenant: %v", err)
	}

	// Thêm thông tin kết quả vào span
	resultAttrs := map[string]interface{}{
		"tenant.count":       len(tenants),
		"tenant.next_cursor": nextCursor,
		"tenant.has_more":    hasMore,
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, resultAttrs)

	var protoTenants []*tenantpb.Tenant
	for _, tenant := range tenants {
		protoTenants = append(protoTenants, convertToProtoTenant(tenant))
	}

	return &tenantpb.ListTenantsResponse{
		Tenants:    protoTenants,
		NextCursor: nextCursor,
		HasMore:    hasMore,
	}, nil
}

func (s *TenantServer) UpdateTenantStatus(ctx context.Context, req *tenantpb.UpdateTenantStatusRequest) (*tenantpb.Tenant, error) {
	// Bắt đầu span mới
	ctx, otSpan, jaegerSpan := tracing.StartTenantSpan(ctx, "grpc.tenant.update_status")
	defer tracing.FinishTenantSpan(otSpan, jaegerSpan)

	// Thêm thuộc tính tenant
	tenantAttrs := map[string]interface{}{
		"tenant.id":        req.Id,
		"tenant.status":    req.Status,
		"tenant.resource":  "tenant",
		"tenant.operation": "update_status",
		"rpc.service":      "TenantService",
		"rpc.method":       "UpdateTenantStatus",
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, tenantAttrs)

	if s.repo == nil {
		err := status.Error(codes.Internal, "repository not initialized")
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, err
	}

	tenant, err := s.repo.UpdateStatus(ctx, uint(req.Id), req.Status)
	if err != nil {
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, status.Errorf(codes.Internal, "không thể cập nhật trạng thái tenant: %v", err)
	}

	return convertToProtoTenant(tenant), nil
}

func (s *TenantServer) GetTenantByCode(ctx context.Context, req *tenantpb.GetTenantByCodeRequest) (*tenantpb.Tenant, error) {
	// Bắt đầu span mới
	ctx, otSpan, jaegerSpan := tracing.StartTenantSpan(ctx, "grpc.tenant.get_by_code")
	defer tracing.FinishTenantSpan(otSpan, jaegerSpan)

	// Thêm thuộc tính tenant
	tenantAttrs := map[string]interface{}{
		"tenant.code":      req.Code,
		"tenant.resource":  "tenant",
		"tenant.operation": "get_by_code",
		"rpc.service":      "TenantService",
		"rpc.method":       "GetTenantByCode",
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, tenantAttrs)

	if s.repo == nil {
		err := status.Error(codes.Internal, "repository not initialized")
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, err
	}

	tenant, err := s.repo.GetByCode(ctx, req.Code)
	if err != nil {
		tracing.RecordTenantError(otSpan, jaegerSpan, err)
		return nil, status.Errorf(codes.NotFound, "không tìm thấy tenant với mã %s: %v", req.Code, err)
	}

	// Thêm thông tin kết quả vào span
	responseAttrs := map[string]interface{}{
		"tenant.id":     tenant.ID,
		"tenant.name":   tenant.Name,
		"tenant.status": tenant.Status,
	}
	tracing.AddTenantAttributes(otSpan, jaegerSpan, responseAttrs)

	return convertToProtoTenant(tenant), nil
}

// Helper function để chuyển đổi từ repository.Tenant sang tenantpb.Tenant
func convertToProtoTenant(t repository.Tenant) *tenantpb.Tenant {
	return &tenantpb.Tenant{
		Id:          int64(t.ID),
		Name:        t.Name,
		Email:       t.Email,
		Code:        t.Code,
		Phone:       t.Phone,
		Address:     t.Address,
		Website:     t.Website,
		Description: t.Description,
		Status:      t.Status,
		CreatedAt:   t.CreatedAt.Format(time.RFC3339),
		UpdatedAt:   t.UpdatedAt.Format(time.RFC3339),
	}
}

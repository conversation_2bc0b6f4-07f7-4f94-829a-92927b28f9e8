package tenant

import (
	"github.com/jmoiron/sqlx"
	tenantpb "github.com/webnew/wn-backend-v2/modules/tenant/grpc/proto"
	"github.com/webnew/wn-backend-v2/modules/tenant/grpc/server"
	"github.com/webnew/wn-backend-v2/modules/tenant/repository/mysql"
	"google.golang.org/grpc"
)

// RegisterGRPC registers the gRPC server for the tenant module
func RegisterGRPC(grpcServer *grpc.Server, db *sqlx.DB) {
	// Khởi tạo repository
	tenantRepo := mysql.NewTenantRepository(db)

	// Khởi tạo server với repository
	tenantServer := server.NewTenantServer()
	tenantServer.SetRepository(tenantRepo)

	tenantpb.RegisterTenantServiceServer(grpcServer, tenantServer)
}

openapi: 3.1.0
info:
  title: Tenant API
  description: API để quản lý tenant trong hệ thống multi-tenant
  version: 1.0.0
servers:
  - url: http://wn-api.local/api/v1
    description: API Server Local
tags:
  - name: Admin Tenants
    description: Quản lý tenant (dành cho admin)
  - name: User Tenants
    description: T<PERSON>y vấn thông tin tenant (dành cho user)

paths:
  /admin/tenants:
    get:
      summary: Lấy danh sách tenant
      description: L<PERSON>y danh sách tất cả tenant trong hệ thống với phân trang
      tags:
        - Admin Tenants
      parameters:
        - in: query
          name: cursor
          schema:
            type: string
          description: Cursor phân trang
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
            maximum: 100
          description: Số lượng kết quả tối đa trả về
        - in: query
          name: status
          schema:
            type: string
            enum: [active, inactive, suspended, trial]
          description: Lọc theo trạng thái
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListTenantsResponse'
        '400':
          description: Dữ liệu không hợp lệ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Không có quyền truy cập
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      summary: Tạo tenant mới
      description: Tạo một tenant mới trong hệ thống
      tags:
        - Admin Tenants
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTenantRequest'
      responses:
        '201':
          description: Tạo tenant thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: Dữ liệu không hợp lệ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Không có quyền truy cập
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /admin/tenants/{tenant_id}:
    get:
      summary: Lấy thông tin chi tiết tenant
      description: Lấy thông tin chi tiết của một tenant theo ID
      tags:
        - Admin Tenants
      parameters:
        - in: path
          name: tenant_id
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
          description: ID của tenant
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantDetailResponse'
        '404':
          description: Không tìm thấy tenant
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Không có quyền truy cập
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      summary: Cập nhật thông tin tenant
      description: Cập nhật thông tin của một tenant
      tags:
        - Admin Tenants
      parameters:
        - in: path
          name: tenant_id
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
          description: ID của tenant
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTenantRequest'
      responses:
        '200':
          description: Cập nhật thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: Dữ liệu không hợp lệ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Không tìm thấy tenant
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Không có quyền truy cập
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      summary: Xóa tenant
      description: Xóa một tenant khỏi hệ thống
      tags:
        - Admin Tenants
      parameters:
        - in: path
          name: tenant_id
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
          description: ID của tenant
      responses:
        '200':
          description: Xóa thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: Không tìm thấy tenant
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Không có quyền truy cập
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /admin/tenants/{tenant_id}/status:
    patch:
      summary: Cập nhật trạng thái tenant
      description: Cập nhật trạng thái của một tenant
      tags:
        - Admin Tenants
      parameters:
        - in: path
          name: tenant_id
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
          description: ID của tenant
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - status
              properties:
                status:
                  type: string
                  enum: [active, inactive, suspended, trial]
                  description: Trạng thái mới của tenant
      responses:
        '200':
          description: Cập nhật trạng thái thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: Dữ liệu không hợp lệ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Không tìm thấy tenant
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Không có quyền truy cập
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /admin/tenants/{tenant_id}/plan:
    patch:
      summary: Cập nhật gói dịch vụ tenant
      description: Cập nhật loại gói dịch vụ và thời hạn của một tenant
      tags:
        - Admin Tenants
      parameters:
        - in: path
          name: tenant_id
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1
          description: ID của tenant
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - plan_type
              properties:
                plan_type:
                  type: string
                  description: Loại gói dịch vụ mới
                  example: "premium"
                subscription_expires_at:
                  type: string
                  format: date-time
                  description: Thời gian hết hạn gói dịch vụ
                  example: "2025-12-31T23:59:59Z"
      responses:
        '200':
          description: Cập nhật gói dịch vụ thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: Dữ liệu không hợp lệ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Không tìm thấy tenant
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Không có quyền truy cập
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /tenants:
    get:
      summary: Lấy danh sách tenant (cho user)
      description: Lấy danh sách tenant trong hệ thống dành cho người dùng
      tags:
        - User Tenants
      parameters:
        - in: query
          name: cursor
          schema:
            type: string
          description: Cursor phân trang
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
            maximum: 100
          description: Số lượng kết quả tối đa trả về
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListTenantsResponse'
        '400':
          description: Dữ liệu không hợp lệ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /tenants/{tenant_code}:
    get:
      summary: Lấy thông tin tenant theo mã
      description: Lấy thông tin chi tiết của một tenant theo mã
      tags:
        - User Tenants
      parameters:
        - in: path
          name: tenant_code
          required: true
          schema:
            type: string
          description: Mã của tenant
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantDetailResponse'
        '404':
          description: Không tìm thấy tenant
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    CreateTenantRequest:
      type: object
      required:
        - tenant_name
        - tenant_code
        - status
        - plan_type
      properties:
        tenant_name:
          type: string
          description: Tên của tenant
          example: "Công ty ABC"
        tenant_code:
          type: string
          description: Mã định danh của tenant (chỉ chứa chữ cái và số)
          example: "company123"
        status:
          type: string
          enum: [active, inactive, suspended, trial]
          description: Trạng thái của tenant
          example: "active"
        plan_type:
          type: string
          description: Loại gói dịch vụ
          example: "premium"
        subscription_expires_at:
          type: string
          format: date-time
          description: Thời gian hết hạn gói dịch vụ
          example: "2025-12-31T23:59:59Z"

    UpdateTenantRequest:
      type: object
      required:
        - tenant_name
        - status
        - plan_type
      properties:
        tenant_name:
          type: string
          description: Tên của tenant
          example: "Công ty ABC (Đã cập nhật)"
        status:
          type: string
          enum: [active, inactive, suspended, trial]
          description: Trạng thái của tenant
          example: "active"
        plan_type:
          type: string
          description: Loại gói dịch vụ
          example: "premium"
        subscription_expires_at:
          type: string
          format: date-time
          description: Thời gian hết hạn gói dịch vụ
          example: "2025-12-31T23:59:59Z"

    TenantResponse:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ID của tenant
          example: 1
        tenant_name:
          type: string
          description: Tên của tenant
          example: "Công ty ABC"
        tenant_code:
          type: string
          description: Mã định danh của tenant
          example: "company123"
        created_at:
          type: string
          format: date-time
          description: Thời gian tạo
          example: "2023-01-01T10:00:00Z"
        updated_at:
          type: string
          format: date-time
          description: Thời gian cập nhật gần nhất
          example: "2023-01-15T14:30:00Z"
        status:
          type: string
          enum: [active, inactive, suspended, trial]
          description: Trạng thái của tenant
          example: "active"
        plan_type:
          type: string
          description: Loại gói dịch vụ
          example: "premium"
        subscription_expires_at:
          type: string
          format: date-time
          description: Thời gian hết hạn gói dịch vụ
          example: "2025-12-31T23:59:59Z"

    TenantDetailResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/StatusResponse'
        data:
          $ref: '#/components/schemas/TenantResponse'

    ListTenantsResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/StatusResponse'
        data:
          type: array
          items:
            $ref: '#/components/schemas/TenantResponse'
        meta:
          type: object
          properties:
            next_cursor:
              type: string
              description: Cursor cho trang tiếp theo
              example: "dGVuYW50X2lkOjEw"
            has_more:
              type: boolean
              description: Có dữ liệu trang tiếp theo hay không
              example: true

    StatusResponse:
      type: object
      properties:
        code:
          type: integer
          description: Mã HTTP status
          example: 200
        message:
          type: string
          description: Thông báo kết quả
          example: "Operation completed successfully"
        success:
          type: boolean
          description: Trạng thái thành công
          example: true
        error_code:
          type: string
          nullable: true
          description: Mã lỗi nếu có
          example: null
        path:
          type: string
          description: Đường dẫn API
          example: "/api/v1/admin/tenants"
        timestamp:
          type: string
          format: date-time
          description: Thời gian phản hồi
          example: "2023-04-15T12:30:45Z"
        details:
          type: array
          nullable: true
          items:
            type: object
            properties:
              field:
                type: string
                description: Trường dữ liệu lỗi
                example: "tenant_code"
              message:
                type: string
                description: Thông báo lỗi
                example: "Mã tenant đã tồn tại"
          description: Chi tiết lỗi nếu có

    SuccessResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/StatusResponse'
        data:
          $ref: '#/components/schemas/TenantResponse'

    ErrorResponse:
      type: object
      properties:
        status:
          type: object
          properties:
            code:
              type: integer
              description: Mã HTTP status
              example: 400
            message:
              type: string
              description: Thông báo lỗi
              example: "Invalid request data"
            success:
              type: boolean
              description: Trạng thái thành công
              example: false
            error_code:
              type: string
              description: Mã lỗi
              example: "INVALID_TENANT_DATA"
            path:
              type: string
              description: Đường dẫn API
              example: "/api/v1/admin/tenants"
            timestamp:
              type: string
              format: date-time
              description: Thời gian phản hồi
              example: "2023-04-15T12:30:45Z"
            details:
              type: array
              items:
                type: object
                properties:
                  field:
                    type: string
                    description: Trường dữ liệu lỗi
                    example: "tenant_code"
                  message:
                    type: string
                    description: Thông báo lỗi
                    example: "Mã tenant đã tồn tại"
              description: Chi tiết lỗi

# Tenant Module

This module provides management for tenants in a multi-tenant system. It supports basic CRUD operations, tenant status management, subscription plan management, and statistics.

## Features

- Create, read, update, and delete tenants
- Tenant lookup by ID or code
- Tenant status management (active, inactive, suspended, trial)
- Subscription plan management with expiry dates
- Tenant statistics
- Pagination using cursor-based approach

## API Endpoints

### CRUD Operations

- `POST /api/v1/tenants` - Create a new tenant
- `GET /api/v1/tenants` - List tenants with cursor pagination
- `GET /api/v1/tenants/:id` - Get a tenant by ID
- `GET /api/v1/tenants/code/:code` - Get a tenant by code
- `PUT /api/v1/tenants/:id` - Update a tenant
- `DELETE /api/v1/tenants/:id` - Delete a tenant

### Management Operations

- `PATCH /api/v1/tenants/:id/status` - Update a tenant's status
- `PATCH /api/v1/tenants/:id/plan` - Update a tenant's subscription plan

### Statistics

- `GET /api/v1/tenants/stats` - Get tenant statistics

## Usage

```go
// Create a new tenant module
db, _ := sqlx.Open("mysql", "dsn")
tenantModule := tenant.NewModule(db)

// Register routes
router := gin.Default()
tenantModule.RegisterRoutes(router)

// Start the service
if err := tenantModule.Start(); err != nil {
    log.Fatal(err)
}

// Stop the service
defer tenantModule.Stop()
```

## Database Schema

The tenant module uses the following database schema:

```sql
CREATE TABLE tenants (
  tenant_id INT PRIMARY KEY AUTO_INCREMENT,
  tenant_name VARCHAR(255) NOT NULL,
  tenant_code VARCHAR(50) NOT NULL UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  status ENUM('active', 'inactive', 'suspended', 'trial') DEFAULT 'active',
  plan_type VARCHAR(50) DEFAULT 'standard',
  subscription_expires_at TIMESTAMP NULL,
  INDEX idx_tenants_status (status),
  INDEX idx_tenants_code (tenant_code)
);
``` 
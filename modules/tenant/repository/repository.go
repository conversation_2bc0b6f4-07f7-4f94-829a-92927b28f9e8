package repository

import (
	"context"
	"time"

	"github.com/webnew/wn-backend-v2/modules/tenant/models"
)

// Tenant represents the entity for gRPC communication
type Tenant struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Email       string    `json:"email"`
	Code        string    `json:"code"`
	Phone       string    `json:"phone"`
	Address     string    `json:"address"`
	Website     string    `json:"website"`
	Description string    `json:"description"`
	Status      string    `json:"status"`
	PlanType    string    `json:"plan_type"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ListParams defines parameters for list operations
type ListParams struct {
	Cursor string `json:"cursor"`
	Limit  int    `json:"limit"`
	Search string `json:"search"`
	Status string `json:"status"`
}

// TenantRepository defines the interface for tenant data operations
type TenantRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, tenant Tenant) (Tenant, error)
	GetByID(ctx context.Context, tenantID uint) (Tenant, error)
	GetByCode(ctx context.Context, code string) (Tenant, error)
	Update(ctx context.Context, tenant Tenant) (Tenant, error)
	Delete(ctx context.Context, tenantID uint) error

	// Check existence
	ExistsByCode(ctx context.Context, code string) (bool, error)

	// List with cursor pagination
	List(ctx context.Context, params ListParams) ([]Tenant, string, bool, error)

	// Status operations
	UpdateStatus(ctx context.Context, tenantID uint, status string) (Tenant, error)

	// Plan operations
	UpdatePlan(ctx context.Context, tenantID uint, planType string, expiresAt *string) error
}

// ConvertModelToTenant chuyển đổi từ models.Tenant sang repository.Tenant
func ConvertModelToTenant(model models.Tenant) Tenant {
	return Tenant{
		ID:        model.TenantID,
		Name:      model.TenantName,
		Code:      model.TenantCode,
		Status:    model.Status,
		PlanType:  model.PlanType,
		CreatedAt: model.CreatedAt,
		UpdatedAt: model.UpdatedAt,
	}
}

// ConvertTenantToModel chuyển đổi từ repository.Tenant sang models.Tenant
func ConvertTenantToModel(tenant Tenant) models.Tenant {
	return models.Tenant{
		TenantID:   tenant.ID,
		TenantName: tenant.Name,
		TenantCode: tenant.Code,
		Status:     tenant.Status,
		PlanType:   tenant.PlanType,
		CreatedAt:  tenant.CreatedAt,
		UpdatedAt:  tenant.UpdatedAt,
	}
}

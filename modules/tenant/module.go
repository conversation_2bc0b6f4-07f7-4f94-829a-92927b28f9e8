package tenant

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"

	"github.com/webnew/wn-backend-v2/modules/tenant/api/handlers/admin"
	"github.com/webnew/wn-backend-v2/modules/tenant/api/handlers/user"
	apiMiddleware "github.com/webnew/wn-backend-v2/modules/tenant/api/middleware"
	"github.com/webnew/wn-backend-v2/modules/tenant/api/routes"
	"github.com/webnew/wn-backend-v2/modules/tenant/configs"
	"github.com/webnew/wn-backend-v2/modules/tenant/repository/mysql"
	adminService "github.com/webnew/wn-backend-v2/modules/tenant/service/admin"
	userService "github.com/webnew/wn-backend-v2/modules/tenant/service/user"
	"github.com/webnew/wn-backend-v2/pkg/auth"
	"github.com/webnew/wn-backend-v2/pkg/middleware"
)

// DummyPermissionService là một triển khai tạm thời cho PermissionService
type DummyPermissionService struct{}

// CheckPermission luôn trả về true, cho phép mọi quyền
func (d *DummyPermissionService) CheckPermission(ctx *gin.Context, tenantID, userID uint, permission string) (bool, error) {
	log.Printf("DummyPermissionService: tenantID=%d, userID=%d, permission=%s", tenantID, userID, permission)
	return true, nil
}

// Module represents the tenant module
type Module struct {
	DB                *sqlx.DB
	Config            *configs.Config
	server            *http.Server
	router            *gin.Engine
	PermissionService middleware.PermissionService
	JWTService        *auth.JWTService
}

// NewModule creates a new instance of the tenant module
func NewModule(db *sqlx.DB) *Module {
	return &Module{
		DB:                db,
		PermissionService: &DummyPermissionService{}, // Sử dụng PermissionService mặc định
	}
}

// NewModuleWithConfig creates a new instance of the tenant module with configuration
func NewModuleWithConfig(cfg *configs.Config) *Module {
	// Set up database connection
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true",
		cfg.DB.Username,
		cfg.DB.Password,
		cfg.DB.Host,
		cfg.DB.Port,
		cfg.DB.Database,
	)

	db, err := sqlx.Connect("mysql", dsn)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Set connection pool settings
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	// Khởi tạo JWT service
	accessTokenExpiration, err := time.ParseDuration(cfg.JWT.AccessTokenExpiration)
	if err != nil {
		log.Printf("Error parsing access token expiration: %v, using default 24h", err)
		accessTokenExpiration = 24 * time.Hour
	}

	refreshTokenExpiration, err := time.ParseDuration(cfg.JWT.RefreshTokenExpiration)
	if err != nil {
		log.Printf("Error parsing refresh token expiration: %v, using default 720h", err)
		refreshTokenExpiration = 720 * time.Hour
	}

	jwtConfig := auth.JWTConfig{
		AccessSigningKey:       cfg.JWT.AccessSigningKey,
		RefreshSigningKey:      cfg.JWT.RefreshSigningKey,
		AccessTokenExpiration:  accessTokenExpiration,
		RefreshTokenExpiration: refreshTokenExpiration,
		Issuer:                 cfg.JWT.Issuer,
	}
	jwtService := auth.NewJWTService(jwtConfig)

	return &Module{
		DB:                db,
		Config:            cfg,
		router:            gin.Default(),
		JWTService:        jwtService,
		PermissionService: &DummyPermissionService{}, // Sử dụng PermissionService mặc định
	}
}

// SetPermissionService sets the permission service for the module
func (m *Module) SetPermissionService(permService middleware.PermissionService) {
	m.PermissionService = permService
}

// RegisterRoutes registers all routes for the tenant module
func (m *Module) RegisterRoutes(router *gin.Engine) {
	// Create repositories
	tenantRepo := mysql.NewTenantRepository(m.DB)

	// Create services
	adminTenantService := adminService.NewTenantService(tenantRepo, m.DB.DB)
	userTenantService := userService.NewTenantService(tenantRepo)

	// Create handlers
	adminTenantHandler := admin.NewTenantHandler(adminTenantService)
	userTenantHandler := user.NewTenantHandler(userTenantService)

	// Register routes
	apiV1 := router.Group("/api/v1")

	// Áp dụng JWT middleware cho toàn bộ API
	apiV1.Use(apiMiddleware.JWTAuth(m.JWTService))

	{
		// Admin routes
		routes.RegisterAdminRoutes(apiV1, adminTenantHandler, m.PermissionService)

		// User routes
		routes.RegisterUserRoutes(apiV1, userTenantHandler, m.PermissionService)
	}
}

// Start starts the tenant module server
func (m *Module) Start() error {
	// If router is not set, use the one from the module
	if m.router == nil {
		m.router = gin.Default()
	}

	// Register routes
	m.RegisterRoutes(m.router)

	// Create HTTP server
	m.server = &http.Server{
		Addr:    fmt.Sprintf("%s:%d", m.Config.Server.Host, m.Config.Server.Port),
		Handler: m.router,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting tenant service on %s:%d", m.Config.Server.Host, m.Config.Server.Port)
		if err := m.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	return nil
}

// Stop gracefully stops the tenant module server
func (m *Module) Stop() error {
	if m.server == nil {
		return nil
	}

	// Create a context with timeout for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Shutdown server
	if err := m.server.Shutdown(ctx); err != nil {
		return fmt.Errorf("server shutdown failed: %w", err)
	}

	// Close database connection
	if m.DB != nil {
		if err := m.DB.Close(); err != nil {
			return fmt.Errorf("database connection close failed: %w", err)
		}
	}

	log.Println("Tenant service stopped gracefully")
	return nil
}

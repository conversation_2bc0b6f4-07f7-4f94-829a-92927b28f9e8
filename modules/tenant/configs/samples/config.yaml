server:
  host: "localhost"
  port: 8081

database:
  host: "localhost"
  port: 3306
  username: "root"
  password: ""
  database: "blog_v4"

tracing:
  enabled: false
  service_name: "tenant-service"
  exporter_type: "jaeger"
  sample_ratio: 1.0
  signoz:
    endpoint: "http://signoz-otel-collector:4317"
  jaeger:
    host: "localhost"
    port: "6831"

jwt:
  access_signing_key: "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
  refresh_signing_key: "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
  access_token_expiration: "168h"
  refresh_token_expiration: "720h"
  issuer: "wn-backend" 
package configs

import (
	"encoding/json"
	"fmt"
	"log"
	"os"

	"github.com/spf13/viper"
)

// DatabaseConfig represents database configuration
type DatabaseConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Database string `mapstructure:"database"`
}

// ServerConfig represents server configuration
type ServerConfig struct {
	Host string `mapstructure:"host"`
	Port int    `mapstructure:"port"`
}

// SignozConfig represents SignOz configuration
type SignozConfig struct {
	Endpoint string `mapstructure:"endpoint"`
}

// JaegerConfig represents Jaeger configuration
type JaegerConfig struct {
	Host string `mapstructure:"host"`
	Port string `mapstructure:"port"`
}

// TracingConfig represents tracing configuration
type TracingConfig struct {
	Enabled      bool         `mapstructure:"enabled"`
	ServiceName  string       `mapstructure:"service_name"`
	ExporterType string       `mapstructure:"exporter_type"`
	SampleRatio  float64      `mapstructure:"sample_ratio"`
	Signoz       SignozConfig `mapstructure:"signoz"`
	J<PERSON><PERSON>       JaegerConfig `mapstructure:"jaeger"`
}

// JWTConfig represents JWT configuration
type JWTConfig struct {
	AccessSigningKey       string `mapstructure:"access_signing_key"`
	RefreshSigningKey      string `mapstructure:"refresh_signing_key"`
	AccessTokenExpiration  string `mapstructure:"access_token_expiration"`
	RefreshTokenExpiration string `mapstructure:"refresh_token_expiration"`
	Issuer                 string `mapstructure:"issuer"`
}

// Config represents the tenant module configuration
type Config struct {
	DB      DatabaseConfig `mapstructure:"database"`
	Server  ServerConfig   `mapstructure:"server"`
	Tracing *TracingConfig `mapstructure:"tracing"`
	JWT     JWTConfig      `mapstructure:"jwt"`
}

// LoadConfig loads configuration from file
func LoadConfig() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("../configs")

	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			log.Println("Config file not found, using default values")
		} else {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Apply environment variables if set
	if os.Getenv("DB_HOST") != "" {
		config.DB.Host = os.Getenv("DB_HOST")
	}
	if os.Getenv("DB_PORT") != "" {
		fmt.Sscanf(os.Getenv("DB_PORT"), "%d", &config.DB.Port)
	}
	if os.Getenv("DB_USER") != "" {
		config.DB.Username = os.Getenv("DB_USER")
	}
	if os.Getenv("DB_PASS") != "" {
		config.DB.Password = os.Getenv("DB_PASS")
	}
	if os.Getenv("DB_NAME") != "" {
		config.DB.Database = os.Getenv("DB_NAME")
	}
	if os.Getenv("SERVER_HOST") != "" {
		config.Server.Host = os.Getenv("SERVER_HOST")
	}
	if os.Getenv("SERVER_PORT") != "" {
		fmt.Sscanf(os.Getenv("SERVER_PORT"), "%d", &config.Server.Port)
	}

	// Tracing environment variables
	if os.Getenv("TRACING_ENABLED") != "" {
		config.Tracing.Enabled = os.Getenv("TRACING_ENABLED") == "true"
	}
	if os.Getenv("TRACING_SERVICE_NAME") != "" {
		config.Tracing.ServiceName = os.Getenv("TRACING_SERVICE_NAME")
	}
	if os.Getenv("TRACING_EXPORTER_TYPE") != "" {
		config.Tracing.ExporterType = os.Getenv("TRACING_EXPORTER_TYPE")
	}
	if os.Getenv("SIGNOZ_ENDPOINT") != "" {
		config.Tracing.Signoz.Endpoint = os.Getenv("SIGNOZ_ENDPOINT")
	}
	if os.Getenv("JAEGER_HOST") != "" {
		config.Tracing.Jaeger.Host = os.Getenv("JAEGER_HOST")
	}
	if os.Getenv("JAEGER_PORT") != "" {
		config.Tracing.Jaeger.Port = os.Getenv("JAEGER_PORT")
	}

	// JWT environment variables
	if os.Getenv("JWT_ACCESS_SIGNING_KEY") != "" {
		config.JWT.AccessSigningKey = os.Getenv("JWT_ACCESS_SIGNING_KEY")
	}
	if os.Getenv("JWT_REFRESH_SIGNING_KEY") != "" {
		config.JWT.RefreshSigningKey = os.Getenv("JWT_REFRESH_SIGNING_KEY")
	}
	if os.Getenv("JWT_ACCESS_TOKEN_EXPIRATION") != "" {
		config.JWT.AccessTokenExpiration = os.Getenv("JWT_ACCESS_TOKEN_EXPIRATION")
	}
	if os.Getenv("JWT_REFRESH_TOKEN_EXPIRATION") != "" {
		config.JWT.RefreshTokenExpiration = os.Getenv("JWT_REFRESH_TOKEN_EXPIRATION")
	}
	if os.Getenv("JWT_ISSUER") != "" {
		config.JWT.Issuer = os.Getenv("JWT_ISSUER")
	}

	// Set default values if they are not set
	if config.DB.Host == "" {
		config.DB.Host = "localhost"
	}
	if config.DB.Port == 0 {
		config.DB.Port = 3306
	}
	if config.DB.Username == "" {
		config.DB.Username = "root"
	}
	if config.DB.Database == "" {
		config.DB.Database = "blog_v4"
	}
	if config.Server.Host == "" {
		config.Server.Host = "localhost"
	}
	if config.Server.Port == 0 {
		config.Server.Port = 8080
	}

	// Set default JWT values if not set
	if config.JWT.AccessSigningKey == "" {
		config.JWT.AccessSigningKey = "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
	}
	if config.JWT.RefreshSigningKey == "" {
		config.JWT.RefreshSigningKey = "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
	}
	if config.JWT.AccessTokenExpiration == "" {
		config.JWT.AccessTokenExpiration = "168h"
	}
	if config.JWT.RefreshTokenExpiration == "" {
		config.JWT.RefreshTokenExpiration = "720h"
	}
	if config.JWT.Issuer == "" {
		config.JWT.Issuer = "wn-backend"
	}

	// Ensure tracing configuration exists
	if config.Tracing == nil {
		config.Tracing = &TracingConfig{
			Enabled:      false,
			ServiceName:  "tenant-service",
			ExporterType: "jaeger",
			SampleRatio:  1.0,
			Signoz: SignozConfig{
				Endpoint: "http://signoz-otel-collector:4317",
			},
			Jaeger: JaegerConfig{
				Host: "localhost",
				Port: "6831",
			},
		}
	}

	// Print config in Docker environment
	if os.Getenv("DOCKER_ENV") == "true" {
		PrintConfig(&config)
	}

	return &config, nil
}

// PrintConfig prints all configuration values in a formatted JSON
func PrintConfig(config *Config) {
	// Create a copy of the config with the password masked for security
	configCopy := *config
	configCopy.DB.Password = "********"
	configCopy.JWT.AccessSigningKey = "********"
	configCopy.JWT.RefreshSigningKey = "********"

	// Marshal config to JSON for pretty printing
	configJSON, err := json.MarshalIndent(configCopy, "", "  ")
	if err != nil {
		fmt.Printf("Error marshaling config: %v\n", err)
		return
	}

	fmt.Printf("=== TENANT MODULE CONFIGURATION ===\n%s\n==============================\n", string(configJSON))
}
